# Live Trading System Configuration - Production

# Trading Parameters
symbol: "XAUUSD!"
timeframe_minutes: 5
prediction_interval_seconds: 30  # Model prediction frequency
execution_interval_seconds: 60   # Trade execution check frequency

# Risk Management
initial_capital: 400.0
max_risk_per_trade: 0.02         # 2% risk per trade
max_concurrent_trades: 3
max_daily_trades: 20
max_daily_loss: 0.05             # 5% max daily loss

# Model-Driven Parameters
min_signal_confidence: 0.65     # Minimum confidence for trade entry
high_confidence_threshold: 0.8   # High confidence threshold
ensemble_consensus_threshold: 0.7 # Minimum ensemble agreement

# Position Sizing (Model-Driven)
base_position_size: 0.1          # Base lot size
confidence_multiplier: 1.5       # Multiply by confidence
volatility_adjustment: true      # Use model volatility adjustment

# Entry/Exit Optimization (Model-Driven)
use_model_entry_timing: true     # Use model for optimal entry timing
use_model_exit_levels: true      # Use model for TP/SL levels
entry_patience_seconds: 300      # Max wait time for optimal entry

# Multi-Tier TP/SL System
tp1_percentage: 0.4              # 40% of position for quick profits
tp2_percentage: 0.35             # 35% for swing profits  
tp3_percentage: 0.25             # 25% for trend following

# Transaction Costs (MT5 Realistic)
spread_pips: 0.18                # Average spread from analysis
slippage_pips: 0.05              # Expected slippage
commission_per_lot: 0.0          # Commission per lot

# MT5 Connection
mt5_timeout_seconds: 30
mt5_retry_attempts: 3
mt5_retry_delay: 1.0

# Data and Models - Updated for Specialized Ensemble
model_path: "data/models/retrained_lightgbm_signal_generator_20250922_001127.pkl"  # Primary model for validation
feature_pipeline_path: "data/models/xauusd_feature_pipeline.joblib"
data_buffer_size: 1000           # Historical data buffer
use_specialized_ensemble: true   # Use the new specialized ensemble architecture

# Performance Monitoring
latency_warning_ms: 50.0         # Warn if latency > 50ms
latency_critical_ms: 100.0       # Critical if latency > 100ms
performance_log_interval: 300    # Log performance every 5 minutes

# Session-Based Trading
trading_sessions:
  asian:
    start_hour: 0
    end_hour: 8
    position_multiplier: 0.8     # Reduce position size
    min_confidence: 0.7          # Higher confidence required
  european:
    start_hour: 8
    end_hour: 16
    position_multiplier: 1.0     # Standard position size
    min_confidence: 0.65         # Standard confidence
  us:
    start_hour: 16
    end_hour: 24
    position_multiplier: 1.2     # Increase position size
    min_confidence: 0.6          # Lower confidence acceptable

# Emergency Controls
emergency_stop_enabled: true
max_consecutive_losses: 5
emergency_stop_loss_pct: 0.1     # 10% portfolio loss triggers stop

# Logging and Monitoring
log_level: "INFO"
log_trades: true
log_predictions: true
log_performance: true
save_trade_history: true
