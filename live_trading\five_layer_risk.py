"""
5-Layer Risk Management System for Live Trading

Implements comprehensive risk management with 5 distinct layers:
Layer 1: Emergency Risk Controls (circuit breakers, system health)
Layer 2: Portfolio Risk Management (exposure limits, correlation risk)
Layer 3: Position Risk Management (individual trade risk, sizing)
Layer 4: Market Timing Risk (volatility, liquidity, session-based)
Layer 5: Cross-Asset Risk (correlation monitoring, regime changes)
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import numpy as np
import pandas as pd
from collections import deque
try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    import logging
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)


class RiskLevel(Enum):
    """Risk severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class RiskAction(Enum):
    """Risk management actions."""
    ALLOW = "allow"
    REDUCE_SIZE = "reduce_size"
    BLOCK_NEW = "block_new"
    CLOSE_POSITIONS = "close_positions"
    EMERGENCY_STOP = "emergency_stop"


@dataclass
class RiskAssessment:
    """Risk assessment result from a layer."""
    layer: int
    layer_name: str
    risk_level: RiskLevel
    risk_score: float  # 0.0 to 1.0
    action: RiskAction
    reasoning: str
    metrics: Dict[str, float]
    timestamp: datetime


@dataclass
class ComprehensiveRiskResult:
    """Complete 5-layer risk assessment result."""
    overall_risk_level: RiskLevel
    overall_risk_score: float
    final_action: RiskAction
    layer_assessments: Dict[int, RiskAssessment]
    position_size_adjustment: float
    max_exposure_allowed: float
    emergency_triggered: bool
    reasoning: str
    timestamp: datetime


class FiveLayerRiskManager(LoggerMixin):
    """
    Comprehensive 5-layer risk management system.
    
    Each layer evaluates different aspects of risk and can override
    lower layers with more restrictive actions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Layer 1: Emergency Risk Controls
        self.max_daily_loss = config.get('max_daily_loss', 0.05)  # 5% max daily loss
        self.max_drawdown = config.get('max_drawdown', 0.10)  # 10% max drawdown
        self.system_health_threshold = config.get('system_health_threshold', 0.8)
        
        # Layer 2: Portfolio Risk Management
        self.max_portfolio_exposure = config.get('max_portfolio_exposure', 0.20)  # 20% max exposure
        self.max_correlation_exposure = config.get('max_correlation_exposure', 0.15)
        self.max_concurrent_trades = config.get('max_concurrent_trades', 3)
        
        # Layer 3: Position Risk Management
        self.max_position_risk = config.get('max_position_risk', 0.02)  # 2% per trade
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 1.5)
        self.max_position_size = config.get('max_position_size', 1.0)
        
        # Layer 4: Market Timing Risk
        self.volatility_threshold = config.get('volatility_threshold', 0.35)
        self.liquidity_threshold = config.get('liquidity_threshold', 0.5)
        self.session_risk_multipliers = config.get('session_risk_multipliers', {
            'asian': 0.8,
            'european': 1.0,
            'us': 1.2,
            'overlap': 1.1
        })
        
        # Layer 5: Cross-Asset Risk
        self.correlation_threshold = config.get('correlation_threshold', 0.8)
        self.regime_change_threshold = config.get('regime_change_threshold', 0.7)
        
        # Risk tracking
        self.risk_history = deque(maxlen=1000)
        self.emergency_stops = []
        self.daily_pnl = 0.0
        self.peak_equity = 0.0
        self.current_drawdown = 0.0
        
        self.logger.info("5-Layer Risk Management System initialized")
    
    def assess_comprehensive_risk(self, trade_signal: Dict[str, Any],
                                current_positions: List[Any],
                                market_data: pd.DataFrame,
                                portfolio_metrics: Dict[str, Any]) -> ComprehensiveRiskResult:
        """
        Perform comprehensive 5-layer risk assessment.
        
        Args:
            trade_signal: Proposed trade signal
            current_positions: List of current positions
            market_data: Current market data
            portfolio_metrics: Portfolio performance metrics
            
        Returns:
            ComprehensiveRiskResult with complete risk analysis
        """
        try:
            timestamp = datetime.now()
            layer_assessments = {}
            
            # Layer 1: Emergency Risk Controls
            layer1 = self._layer1_emergency_controls(
                trade_signal, current_positions, portfolio_metrics
            )
            layer_assessments[1] = layer1
            
            if layer1.action == RiskAction.EMERGENCY_STOP:
                return self._create_emergency_result(layer1, layer_assessments, timestamp)
            
            # Layer 2: Portfolio Risk Management
            layer2 = self._layer2_portfolio_risk(
                trade_signal, current_positions, portfolio_metrics
            )
            layer_assessments[2] = layer2
            
            if layer2.action in [RiskAction.EMERGENCY_STOP, RiskAction.CLOSE_POSITIONS]:
                return self._create_restrictive_result(layer2, layer_assessments, timestamp)
            
            # Layer 3: Position Risk Management
            layer3 = self._layer3_position_risk(
                trade_signal, current_positions, market_data
            )
            layer_assessments[3] = layer3
            
            # Layer 4: Market Timing Risk
            layer4 = self._layer4_market_timing(
                trade_signal, market_data, portfolio_metrics
            )
            layer_assessments[4] = layer4
            
            # Layer 5: Cross-Asset Risk
            layer5 = self._layer5_cross_asset(
                trade_signal, market_data, portfolio_metrics
            )
            layer_assessments[5] = layer5
            
            # Integrate all layers
            final_result = self._integrate_risk_layers(layer_assessments, timestamp)
            
            # Store in history
            self.risk_history.append(final_result)
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"Comprehensive risk assessment failed: {str(e)}")
            return self._create_error_result(str(e), timestamp)
    
    def _layer1_emergency_controls(self, trade_signal: Dict[str, Any],
                                 current_positions: List[Any],
                                 portfolio_metrics: Dict[str, Any]) -> RiskAssessment:
        """Layer 1: Emergency Risk Controls."""
        
        risk_factors = []
        risk_score = 0.0
        
        # Check daily loss limit
        daily_pnl = portfolio_metrics.get('daily_pnl', 0.0)
        if daily_pnl < -self.max_daily_loss:
            risk_factors.append(f"Daily loss {daily_pnl:.3f} exceeds limit {self.max_daily_loss}")
            risk_score += 0.4
        
        # Check maximum drawdown
        current_drawdown = portfolio_metrics.get('current_drawdown', 0.0)
        if current_drawdown > self.max_drawdown:
            risk_factors.append(f"Drawdown {current_drawdown:.3f} exceeds limit {self.max_drawdown}")
            risk_score += 0.5
        
        # Check system health
        system_health = portfolio_metrics.get('system_health_score', 1.0)
        if system_health < self.system_health_threshold:
            risk_factors.append(f"System health {system_health:.3f} below threshold")
            risk_score += 0.3
        
        # Determine action
        if risk_score >= 0.8:
            action = RiskAction.EMERGENCY_STOP
            risk_level = RiskLevel.EMERGENCY
        elif risk_score >= 0.6:
            action = RiskAction.CLOSE_POSITIONS
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.4:
            action = RiskAction.BLOCK_NEW
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.MEDIUM
        else:
            action = RiskAction.ALLOW
            risk_level = RiskLevel.LOW
        
        return RiskAssessment(
            layer=1,
            layer_name="Emergency Controls",
            risk_level=risk_level,
            risk_score=risk_score,
            action=action,
            reasoning="; ".join(risk_factors) if risk_factors else "Emergency controls passed",
            metrics={
                'daily_pnl': daily_pnl,
                'current_drawdown': current_drawdown,
                'system_health': system_health
            },
            timestamp=datetime.now()
        )
    
    def _layer2_portfolio_risk(self, trade_signal: Dict[str, Any],
                             current_positions: List[Any],
                             portfolio_metrics: Dict[str, Any]) -> RiskAssessment:
        """Layer 2: Portfolio Risk Management."""
        
        risk_factors = []
        risk_score = 0.0
        
        # Check portfolio exposure
        current_exposure = portfolio_metrics.get('total_exposure', 0.0)
        if current_exposure > self.max_portfolio_exposure:
            risk_factors.append(f"Portfolio exposure {current_exposure:.3f} exceeds limit")
            risk_score += 0.3
        
        # Check number of concurrent trades
        active_trades = len(current_positions)
        if active_trades >= self.max_concurrent_trades:
            risk_factors.append(f"Too many concurrent trades: {active_trades}")
            risk_score += 0.2
        
        # Check correlation exposure
        correlation_exposure = portfolio_metrics.get('correlation_exposure', 0.0)
        if correlation_exposure > self.max_correlation_exposure:
            risk_factors.append(f"High correlation exposure: {correlation_exposure:.3f}")
            risk_score += 0.25
        
        # Determine action
        if risk_score >= 0.6:
            action = RiskAction.CLOSE_POSITIONS
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.4:
            action = RiskAction.BLOCK_NEW
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.2:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.MEDIUM
        else:
            action = RiskAction.ALLOW
            risk_level = RiskLevel.LOW
        
        return RiskAssessment(
            layer=2,
            layer_name="Portfolio Risk",
            risk_level=risk_level,
            risk_score=risk_score,
            action=action,
            reasoning="; ".join(risk_factors) if risk_factors else "Portfolio risk acceptable",
            metrics={
                'current_exposure': current_exposure,
                'active_trades': active_trades,
                'correlation_exposure': correlation_exposure
            },
            timestamp=datetime.now()
        )

    def _layer5_cross_asset(self, trade_signal: Dict[str, Any],
                          market_data: pd.DataFrame,
                          portfolio_metrics: Dict[str, Any]) -> RiskAssessment:
        """Layer 5: Cross-Asset Risk."""

        risk_factors = []
        risk_score = 0.0

        # Check cross-asset correlations (simplified - would need actual cross-asset data)
        dxy_correlation = portfolio_metrics.get('dxy_correlation', 0.0)
        if abs(dxy_correlation) > self.correlation_threshold:
            risk_factors.append(f"High DXY correlation: {dxy_correlation:.3f}")
            risk_score += 0.2

        # Check regime change indicators
        regime_stability = portfolio_metrics.get('regime_stability', 1.0)
        if regime_stability < self.regime_change_threshold:
            risk_factors.append(f"Regime instability: {regime_stability:.3f}")
            risk_score += 0.3

        # Check VIX spike (if available)
        vix_spike = portfolio_metrics.get('vix_spike_detected', False)
        if vix_spike:
            risk_factors.append("VIX spike detected")
            risk_score += 0.25

        # Determine action
        if risk_score >= 0.5:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.MEDIUM
        elif risk_score >= 0.3:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.LOW
        else:
            action = RiskAction.ALLOW
            risk_level = RiskLevel.LOW

        return RiskAssessment(
            layer=5,
            layer_name="Cross-Asset Risk",
            risk_level=risk_level,
            risk_score=risk_score,
            action=action,
            reasoning="; ".join(risk_factors) if risk_factors else "Cross-asset risk acceptable",
            metrics={
                'dxy_correlation': dxy_correlation,
                'regime_stability': regime_stability,
                'vix_spike': vix_spike
            },
            timestamp=datetime.now()
        )

    def _integrate_risk_layers(self, layer_assessments: Dict[int, RiskAssessment],
                             timestamp: datetime) -> ComprehensiveRiskResult:
        """Integrate all risk layer assessments into final result."""

        # Find most restrictive action
        actions_hierarchy = [
            RiskAction.EMERGENCY_STOP,
            RiskAction.CLOSE_POSITIONS,
            RiskAction.BLOCK_NEW,
            RiskAction.REDUCE_SIZE,
            RiskAction.ALLOW
        ]

        final_action = RiskAction.ALLOW
        for action in actions_hierarchy:
            if any(assessment.action == action for assessment in layer_assessments.values()):
                final_action = action
                break

        # Calculate overall risk score (weighted average)
        layer_weights = {1: 0.3, 2: 0.25, 3: 0.2, 4: 0.15, 5: 0.1}
        overall_risk_score = sum(
            layer_assessments[layer].risk_score * weight
            for layer, weight in layer_weights.items()
            if layer in layer_assessments
        )

        # Determine overall risk level
        if overall_risk_score >= 0.8:
            overall_risk_level = RiskLevel.EMERGENCY
        elif overall_risk_score >= 0.6:
            overall_risk_level = RiskLevel.CRITICAL
        elif overall_risk_score >= 0.4:
            overall_risk_level = RiskLevel.HIGH
        elif overall_risk_score >= 0.2:
            overall_risk_level = RiskLevel.MEDIUM
        else:
            overall_risk_level = RiskLevel.LOW

        # Calculate position size adjustment
        position_size_adjustment = self._calculate_position_adjustment(layer_assessments)

        # Calculate max exposure allowed
        max_exposure_allowed = self._calculate_max_exposure(layer_assessments)

        # Check for emergency
        emergency_triggered = final_action == RiskAction.EMERGENCY_STOP

        # Create reasoning
        reasoning_parts = []
        for layer, assessment in layer_assessments.items():
            if assessment.risk_score > 0.1:
                reasoning_parts.append(f"L{layer}: {assessment.reasoning}")

        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "All risk layers acceptable"

        return ComprehensiveRiskResult(
            overall_risk_level=overall_risk_level,
            overall_risk_score=overall_risk_score,
            final_action=final_action,
            layer_assessments=layer_assessments,
            position_size_adjustment=position_size_adjustment,
            max_exposure_allowed=max_exposure_allowed,
            emergency_triggered=emergency_triggered,
            reasoning=reasoning,
            timestamp=timestamp
        )

    def _calculate_position_adjustment(self, layer_assessments: Dict[int, RiskAssessment]) -> float:
        """Calculate position size adjustment based on risk layers."""
        adjustment = 1.0

        for assessment in layer_assessments.values():
            if assessment.action == RiskAction.REDUCE_SIZE:
                # Reduce based on risk score
                reduction = 1.0 - (assessment.risk_score * 0.5)
                adjustment = min(adjustment, reduction)

        return max(0.1, adjustment)  # Minimum 10% of original size

    def _calculate_max_exposure(self, layer_assessments: Dict[int, RiskAssessment]) -> float:
        """Calculate maximum allowed exposure based on risk layers."""
        base_exposure = self.max_portfolio_exposure

        for assessment in layer_assessments.values():
            if assessment.risk_score > 0.3:
                # Reduce max exposure based on risk
                reduction = 1.0 - (assessment.risk_score * 0.3)
                base_exposure *= reduction

        return max(0.05, base_exposure)  # Minimum 5% exposure

    def _get_trading_session(self, hour: int) -> str:
        """Determine trading session based on hour."""
        if 0 <= hour < 8:
            return 'asian'
        elif 8 <= hour < 16:
            return 'european'
        elif 16 <= hour < 24:
            return 'us'
        else:
            return 'asian'

    def _create_emergency_result(self, emergency_assessment: RiskAssessment,
                               layer_assessments: Dict[int, RiskAssessment],
                               timestamp: datetime) -> ComprehensiveRiskResult:
        """Create emergency risk result."""
        return ComprehensiveRiskResult(
            overall_risk_level=RiskLevel.EMERGENCY,
            overall_risk_score=1.0,
            final_action=RiskAction.EMERGENCY_STOP,
            layer_assessments=layer_assessments,
            position_size_adjustment=0.0,
            max_exposure_allowed=0.0,
            emergency_triggered=True,
            reasoning=f"EMERGENCY: {emergency_assessment.reasoning}",
            timestamp=timestamp
        )

    def _create_restrictive_result(self, restrictive_assessment: RiskAssessment,
                                 layer_assessments: Dict[int, RiskAssessment],
                                 timestamp: datetime) -> ComprehensiveRiskResult:
        """Create restrictive risk result."""
        return ComprehensiveRiskResult(
            overall_risk_level=RiskLevel.CRITICAL,
            overall_risk_score=0.8,
            final_action=restrictive_assessment.action,
            layer_assessments=layer_assessments,
            position_size_adjustment=0.1,
            max_exposure_allowed=0.05,
            emergency_triggered=False,
            reasoning=f"CRITICAL: {restrictive_assessment.reasoning}",
            timestamp=timestamp
        )

    def _create_error_result(self, error_msg: str, timestamp: datetime) -> ComprehensiveRiskResult:
        """Create error risk result."""
        return ComprehensiveRiskResult(
            overall_risk_level=RiskLevel.CRITICAL,
            overall_risk_score=1.0,
            final_action=RiskAction.BLOCK_NEW,
            layer_assessments={},
            position_size_adjustment=0.0,
            max_exposure_allowed=0.0,
            emergency_triggered=False,
            reasoning=f"Risk assessment error: {error_msg}",
            timestamp=timestamp
        )

    def update_portfolio_metrics(self, daily_pnl: float, current_equity: float):
        """Update portfolio metrics for risk assessment."""
        self.daily_pnl = daily_pnl

        # Update peak equity and drawdown
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.peak_equity - current_equity) / self.peak_equity

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get current risk management summary."""
        recent_assessments = list(self.risk_history)[-10:] if self.risk_history else []

        return {
            'current_drawdown': self.current_drawdown,
            'daily_pnl': self.daily_pnl,
            'peak_equity': self.peak_equity,
            'recent_risk_scores': [r.overall_risk_score for r in recent_assessments],
            'emergency_stops_count': len(self.emergency_stops),
            'last_assessment': recent_assessments[-1].__dict__ if recent_assessments else None
        }
    
    def _layer3_position_risk(self, trade_signal: Dict[str, Any],
                            current_positions: List[Any],
                            market_data: pd.DataFrame) -> RiskAssessment:
        """Layer 3: Position Risk Management."""
        
        risk_factors = []
        risk_score = 0.0
        
        # Check position size
        position_size = trade_signal.get('position_size_multiplier', 1.0)
        if position_size > self.max_position_size:
            risk_factors.append(f"Position size {position_size:.3f} too large")
            risk_score += 0.2
        
        # Check risk-reward ratio
        tp_distance = trade_signal.get('tp1_distance', 15.0)
        sl_distance = trade_signal.get('sl_distance', 20.0)
        if sl_distance > 0:
            risk_reward = tp_distance / sl_distance
            if risk_reward < self.min_risk_reward_ratio:
                risk_factors.append(f"Poor risk-reward ratio: {risk_reward:.2f}")
                risk_score += 0.3
        
        # Check position risk percentage
        account_balance = 10000  # Should come from actual account
        risk_amount = (sl_distance * 0.1 * position_size * 100) / account_balance  # Rough calculation
        if risk_amount > self.max_position_risk:
            risk_factors.append(f"Position risk {risk_amount:.3f} exceeds limit")
            risk_score += 0.4
        
        # Determine action
        if risk_score >= 0.6:
            action = RiskAction.BLOCK_NEW
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.3:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.MEDIUM
        else:
            action = RiskAction.ALLOW
            risk_level = RiskLevel.LOW
        
        return RiskAssessment(
            layer=3,
            layer_name="Position Risk",
            risk_level=risk_level,
            risk_score=risk_score,
            action=action,
            reasoning="; ".join(risk_factors) if risk_factors else "Position risk acceptable",
            metrics={
                'position_size': position_size,
                'risk_reward_ratio': tp_distance / max(sl_distance, 1.0),
                'estimated_risk_pct': risk_amount
            },
            timestamp=datetime.now()
        )
    
    def _layer4_market_timing(self, trade_signal: Dict[str, Any],
                            market_data: pd.DataFrame,
                            portfolio_metrics: Dict[str, Any]) -> RiskAssessment:
        """Layer 4: Market Timing Risk."""
        
        risk_factors = []
        risk_score = 0.0
        
        # Check volatility
        returns = market_data['close'].pct_change().dropna()
        current_vol = returns.tail(20).std() * np.sqrt(1440)  # Annualized
        if current_vol > self.volatility_threshold:
            risk_factors.append(f"High volatility: {current_vol:.3f}")
            risk_score += 0.3
        
        # Check session timing
        current_hour = datetime.now().hour
        session = self._get_trading_session(current_hour)
        session_multiplier = self.session_risk_multipliers.get(session, 1.0)
        if session_multiplier < 0.9:
            risk_factors.append(f"Unfavorable session: {session}")
            risk_score += 0.1
        
        # Check liquidity (simplified)
        volume_ma = market_data['volume'].tail(20).mean() if 'volume' in market_data.columns else 1000
        current_volume = market_data['volume'].iloc[-1] if 'volume' in market_data.columns else 1000
        liquidity_ratio = current_volume / max(volume_ma, 1)
        if liquidity_ratio < self.liquidity_threshold:
            risk_factors.append(f"Low liquidity: {liquidity_ratio:.3f}")
            risk_score += 0.2
        
        # Determine action
        if risk_score >= 0.4:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.MEDIUM
        elif risk_score >= 0.2:
            action = RiskAction.REDUCE_SIZE
            risk_level = RiskLevel.LOW
        else:
            action = RiskAction.ALLOW
            risk_level = RiskLevel.LOW
        
        return RiskAssessment(
            layer=4,
            layer_name="Market Timing",
            risk_level=risk_level,
            risk_score=risk_score,
            action=action,
            reasoning="; ".join(risk_factors) if risk_factors else "Market timing favorable",
            metrics={
                'volatility': current_vol,
                'session': session,
                'session_multiplier': session_multiplier,
                'liquidity_ratio': liquidity_ratio
            },
            timestamp=datetime.now()
        )
