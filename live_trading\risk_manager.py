"""
Live Risk Management System

Provides comprehensive risk management with model integration,
dynamic position sizing, and portfolio risk controls.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from collections import deque

from .base import LiveTrade, TradeDirection, TradeStatus
from .account_balance_manager import AccountBalanceManager

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin


class LiveRiskManager(LoggerMixin):
    """
    Advanced risk management system for live trading.
    
    Integrates with model predictions to provide dynamic risk controls,
    position sizing, and portfolio protection.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize live risk manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Risk parameters
        self.initial_capital = config.get('initial_capital', 100000.0)  # Fallback only
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)
        self.max_concurrent_trades = config.get('max_concurrent_trades', 3)
        self.max_daily_trades = config.get('max_daily_trades', 20)
        self.max_daily_loss = config.get('max_daily_loss', 0.05)

        # Emergency controls
        self.emergency_stop_enabled = config.get('emergency_stop_enabled', True)
        self.max_consecutive_losses = config.get('max_consecutive_losses', 5)
        self.emergency_stop_loss_pct = config.get('emergency_stop_loss_pct', 0.1)

        # Account balance manager
        self.balance_manager = AccountBalanceManager(config)

        # Current state - initialize with real balance
        self._initialize_balance()
        self.daily_pnl = 0.0
        self.daily_trades_count = 0
        self.consecutive_losses = 0
        self.last_reset_date = datetime.now().date()
        
        # Risk tracking
        self.risk_metrics = {
            'total_exposure': 0.0,
            'max_drawdown': 0.0,
            'current_drawdown': 0.0,
            'peak_balance': self.initial_capital,
            'var_95': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'risk_adjusted_return': 0.0
        }
        
        # Trade history for risk calculations
        self.trade_history = deque(maxlen=1000)
        self.daily_pnl_history = deque(maxlen=252)  # 1 year of trading days
        
        # Risk alerts
        self.risk_alerts = []
        self.emergency_stop_triggered = False

    def _initialize_balance(self):
        """Initialize current balance from real account data."""
        try:
            # Connect balance manager
            if not self.balance_manager.connect():
                self.logger.warning("Failed to connect balance manager, using fallback")

            # Get real account balance
            account_info = self.balance_manager.get_account_info()
            self.current_balance = account_info.balance

            self.logger.info(f"Risk manager initialized with balance: ${self.current_balance:,.2f} ({account_info.source})")

            # Update initial capital to match real balance for calculations
            if account_info.source == 'mt5':
                self.initial_capital = self.current_balance
                self.logger.info(f"Updated initial capital to real balance: ${self.initial_capital:,.2f}")

        except Exception as e:
            self.logger.error(f"Failed to initialize real balance: {str(e)}")
            self.current_balance = self.initial_capital
            self.logger.warning(f"Using fallback balance: ${self.current_balance:,.2f}")

    def refresh_balance(self) -> float:
        """Refresh current balance from account manager."""
        try:
            account_info = self.balance_manager.get_account_info(force_refresh=True)
            old_balance = self.current_balance
            self.current_balance = account_info.balance

            if abs(self.current_balance - old_balance) > 0.01:  # Only log if significant change
                self.logger.info(f"Balance updated: ${old_balance:,.2f} → ${self.current_balance:,.2f}")

            return self.current_balance

        except Exception as e:
            self.logger.error(f"Failed to refresh balance: {str(e)}")
            return self.current_balance
    
    def validate_trade_risk(self, signal: Dict[str, Any],
                          current_balance: float,
                          active_trades: List[LiveTrade]) -> Dict[str, Any]:
        """
        Validate if trade meets risk criteria.

        Args:
            signal: Trading signal from model
            current_balance: Current account balance (may be overridden with real balance)
            active_trades: List of active trades

        Returns:
            Dictionary with validation result and risk assessment
        """
        # Refresh balance from account manager for most accurate risk calculation
        real_balance = self.refresh_balance()

        # Use real balance if significantly different from passed balance
        if abs(real_balance - current_balance) > (current_balance * 0.01):  # 1% difference
            self.logger.info(f"Using real balance ${real_balance:,.2f} instead of passed ${current_balance:,.2f}")
            current_balance = real_balance
        self._update_daily_stats()
        
        validation_result = {
            'approved': True,
            'risk_score': 0.0,
            'warnings': [],
            'rejections': [],
            'position_size_adjustment': 1.0,
            'max_position_size': 0.0
        }
        
        # Check emergency stop
        if self.emergency_stop_triggered:
            validation_result['approved'] = False
            validation_result['rejections'].append("Emergency stop is active")
            return validation_result
        
        # Check daily limits
        if self.daily_trades_count >= self.max_daily_trades:
            validation_result['approved'] = False
            validation_result['rejections'].append(f"Daily trade limit reached: {self.max_daily_trades}")
        
        # Check daily loss limit
        daily_loss_pct = abs(self.daily_pnl) / current_balance
        if self.daily_pnl < 0 and daily_loss_pct >= self.max_daily_loss:
            validation_result['approved'] = False
            validation_result['rejections'].append(f"Daily loss limit reached: {daily_loss_pct:.2%}")
        
        # Check concurrent trades limit
        if len(active_trades) >= self.max_concurrent_trades:
            validation_result['approved'] = False
            validation_result['rejections'].append(f"Maximum concurrent trades reached: {self.max_concurrent_trades}")
        
        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            validation_result['warnings'].append(f"High consecutive losses: {self.consecutive_losses}")
            validation_result['position_size_adjustment'] *= 0.5  # Reduce position size
        
        # Calculate maximum position size
        max_risk_amount = current_balance * self.max_risk_per_trade
        sl_distance = signal.get('sl_distance', 20.0)
        max_position_size = max_risk_amount / (sl_distance * 1.0)  # $1 per pip per 0.01 lot
        validation_result['max_position_size'] = max_position_size
        
        # Model-driven risk adjustments
        confidence = signal.get('confidence', 0.0)
        market_regime = signal.get('market_regime', 'unknown')
        volatility_level = signal.get('volatility_level', 'normal')
        
        # Adjust position size based on model confidence
        if confidence < 0.7:
            validation_result['position_size_adjustment'] *= 0.8
            validation_result['warnings'].append("Low confidence signal - reduced position size")
        elif confidence > 0.85:
            validation_result['position_size_adjustment'] *= 1.2
            validation_result['warnings'].append("High confidence signal - increased position size")
        
        # Adjust for market regime
        if market_regime in ['ranging_low', 'ranging_high']:
            validation_result['position_size_adjustment'] *= 0.9
            validation_result['warnings'].append("Ranging market - reduced position size")
        
        # Adjust for volatility
        if volatility_level == 'high':
            validation_result['position_size_adjustment'] *= 0.8
            validation_result['warnings'].append("High volatility - reduced position size")
        
        # Calculate risk score (0-100)
        risk_factors = [
            daily_loss_pct * 100,
            (len(active_trades) / self.max_concurrent_trades) * 20,
            (self.consecutive_losses / self.max_consecutive_losses) * 30,
            (1 - confidence) * 25,
            25 if volatility_level == 'high' else 0
        ]
        validation_result['risk_score'] = min(100, sum(risk_factors))
        
        # High risk warning
        if validation_result['risk_score'] > 70:
            validation_result['warnings'].append(f"High risk score: {validation_result['risk_score']:.1f}")
        
        return validation_result
    
    def update_trade_result(self, trade: LiveTrade):
        """Update risk metrics with completed trade."""
        if trade.status != TradeStatus.CLOSED:
            return
        
        # Update balance and daily P&L
        self.current_balance += trade.realized_pnl
        self.daily_pnl += trade.realized_pnl
        
        # Update consecutive losses
        if trade.realized_pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0
        
        # Add to trade history
        self.trade_history.append({
            'timestamp': trade.exit_time,
            'pnl': trade.realized_pnl,
            'confidence': trade.model_confidence,
            'duration_minutes': (trade.exit_time - trade.entry_time).total_seconds() / 60,
            'max_favorable_excursion': trade.max_favorable_excursion,
            'max_adverse_excursion': trade.max_adverse_excursion
        })
        
        # Update risk metrics
        self._update_risk_metrics()
        
        # Check emergency stop conditions
        self._check_emergency_stop()
        
        self.logger.info(f"Risk metrics updated - Balance: ${self.current_balance:.2f}, "
                        f"Daily P&L: ${self.daily_pnl:.2f}, Consecutive losses: {self.consecutive_losses}")
    
    def _update_daily_stats(self):
        """Update daily statistics and reset if new day."""
        current_date = datetime.now().date()
        
        if current_date != self.last_reset_date:
            # New day - reset daily stats
            if self.daily_pnl != 0:
                self.daily_pnl_history.append(self.daily_pnl)
            
            self.daily_pnl = 0.0
            self.daily_trades_count = 0
            self.last_reset_date = current_date
            
            self.logger.info(f"Daily stats reset for {current_date}")
    
    def _update_risk_metrics(self):
        """Update comprehensive risk metrics."""
        if len(self.trade_history) < 2:
            return
        
        # Convert trade history to arrays
        pnls = np.array([t['pnl'] for t in self.trade_history])
        
        # Update peak balance and drawdown
        if self.current_balance > self.risk_metrics['peak_balance']:
            self.risk_metrics['peak_balance'] = self.current_balance
        
        self.risk_metrics['current_drawdown'] = (
            (self.risk_metrics['peak_balance'] - self.current_balance) / 
            self.risk_metrics['peak_balance']
        )
        
        if self.risk_metrics['current_drawdown'] > self.risk_metrics['max_drawdown']:
            self.risk_metrics['max_drawdown'] = self.risk_metrics['current_drawdown']
        
        # Calculate win rate
        winning_trades = np.sum(pnls > 0)
        total_trades = len(pnls)
        self.risk_metrics['win_rate'] = winning_trades / total_trades
        
        # Calculate profit factor
        gross_profit = np.sum(pnls[pnls > 0])
        gross_loss = abs(np.sum(pnls[pnls < 0]))
        
        if gross_loss > 0:
            self.risk_metrics['profit_factor'] = gross_profit / gross_loss
        else:
            self.risk_metrics['profit_factor'] = float('inf') if gross_profit > 0 else 0.0
        
        # Calculate Sharpe ratio (simplified)
        if len(pnls) > 10:
            returns = pnls / self.initial_capital
            if np.std(returns) > 0:
                self.risk_metrics['sharpe_ratio'] = np.mean(returns) / np.std(returns) * np.sqrt(252)
        
        # Calculate VaR (95% confidence)
        if len(pnls) > 20:
            self.risk_metrics['var_95'] = np.percentile(pnls, 5)
        
        # Risk-adjusted return
        total_return = (self.current_balance - self.initial_capital) / self.initial_capital
        if self.risk_metrics['max_drawdown'] > 0:
            self.risk_metrics['risk_adjusted_return'] = total_return / self.risk_metrics['max_drawdown']
    
    def _check_emergency_stop(self):
        """Check if emergency stop should be triggered."""
        if not self.emergency_stop_enabled or self.emergency_stop_triggered:
            return
        
        # Check portfolio loss threshold
        portfolio_loss_pct = (self.initial_capital - self.current_balance) / self.initial_capital
        
        if portfolio_loss_pct >= self.emergency_stop_loss_pct:
            self.emergency_stop_triggered = True
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'EMERGENCY_STOP',
                'message': f"Emergency stop triggered - Portfolio loss: {portfolio_loss_pct:.2%}",
                'severity': 'CRITICAL'
            })
            self.logger.critical(f"🚨 EMERGENCY STOP TRIGGERED - Portfolio loss: {portfolio_loss_pct:.2%}")
        
        # Check consecutive losses
        elif self.consecutive_losses >= self.max_consecutive_losses:
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'HIGH_CONSECUTIVE_LOSSES',
                'message': f"High consecutive losses: {self.consecutive_losses}",
                'severity': 'HIGH'
            })
            self.logger.warning(f"⚠️ High consecutive losses: {self.consecutive_losses}")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        return {
            'current_balance': self.current_balance,
            'daily_pnl': self.daily_pnl,
            'daily_trades_count': self.daily_trades_count,
            'consecutive_losses': self.consecutive_losses,
            'emergency_stop_triggered': self.emergency_stop_triggered,
            'risk_metrics': self.risk_metrics,
            'risk_limits': {
                'max_risk_per_trade': self.max_risk_per_trade,
                'max_concurrent_trades': self.max_concurrent_trades,
                'max_daily_trades': self.max_daily_trades,
                'max_daily_loss': self.max_daily_loss
            },
            'recent_alerts': self.risk_alerts[-10:],  # Last 10 alerts
            'trade_history_size': len(self.trade_history)
        }
    
    def reset_emergency_stop(self) -> bool:
        """Reset emergency stop (manual intervention required)."""
        if self.emergency_stop_triggered:
            self.emergency_stop_triggered = False
            self.consecutive_losses = 0
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'EMERGENCY_STOP_RESET',
                'message': "Emergency stop manually reset",
                'severity': 'INFO'
            })
            self.logger.info("Emergency stop manually reset")
            return True
        return False
    
    def get_position_size_recommendation(self, signal: Dict[str, Any], 
                                       current_balance: float) -> float:
        """Get recommended position size based on risk analysis."""
        validation = self.validate_trade_risk(signal, current_balance, [])
        
        if not validation['approved']:
            return 0.0
        
        max_size = validation['max_position_size']
        adjustment = validation['position_size_adjustment']
        
        return max_size * adjustment

    def can_open_position(self, signal: Dict[str, Any]) -> bool:
        """
        Legacy method for backward compatibility.
        Check if a position can be opened based on risk criteria.

        Args:
            signal: Trading signal dictionary

        Returns:
            bool: True if position can be opened, False otherwise
        """
        try:
            # Get current balance
            current_balance = self.balance_manager.get_balance() if self.balance_manager else 1000.0

            # Use the comprehensive risk validation
            validation = self.validate_trade_risk(signal, current_balance, [])

            # Return approval status
            approved = validation.get('approved', False)

            if not approved:
                self.logger.info(f"Position blocked by risk manager: {validation.get('reason', 'Unknown reason')}")

            return approved

        except Exception as e:
            self.logger.error(f"Error in can_open_position: {str(e)}")
            return False  # Conservative approach - block trade on error
