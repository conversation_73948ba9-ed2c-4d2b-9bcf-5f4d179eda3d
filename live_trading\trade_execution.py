"""
Trade Execution Engine

Handles MT5 trade execution with model-driven entry/exit optimization,
multi-tier TP/SL system, and comprehensive position management.
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import time
import uuid

from .base import LiveTrade, TradeDirection, TradeStatus, OrderType
from .account_balance_manager import AccountBalanceManager

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.mt5_collector.mt5_client import MT5Client
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import MT5DataError


class TradeExecutionEngine(LoggerMixin):
    """
    Advanced trade execution engine with model-driven optimization.
    
    Implements sophisticated entry/exit logic, multi-tier TP/SL system,
    and real-time position management with MT5 integration.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize trade execution engine.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.symbol = config.get('symbol', 'XAUUSD!')
        
        # MT5 client
        self.client = MT5Client()
        self.is_connected = False

        # Account balance manager
        self.balance_manager = AccountBalanceManager(config)
        
        # Execution parameters
        self.spread_pips = config.get('spread_pips', 0.18)
        self.slippage_pips = config.get('slippage_pips', 0.05)
        self.max_slippage_pips = config.get('max_slippage_pips', 0.5)
        
        # Multi-tier TP/SL configuration
        self.tp1_percentage = config.get('tp1_percentage', 0.4)  # 40% quick profits
        self.tp2_percentage = config.get('tp2_percentage', 0.35) # 35% swing profits
        self.tp3_percentage = config.get('tp3_percentage', 0.25) # 25% trend following
        
        # Model-driven parameters
        self.use_model_entry_timing = config.get('use_model_entry_timing', True)
        self.use_model_exit_levels = config.get('use_model_exit_levels', True)
        self.entry_patience_seconds = config.get('entry_patience_seconds', 300)
        
        # Execution tracking
        self.execution_stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'avg_execution_time_ms': 0.0,
            'avg_slippage_pips': 0.0,
            'total_spread_cost': 0.0,
            'total_commission': 0.0,
            'last_execution_time': None,
            'errors': 0
        }
        
        # Active orders tracking
        self.pending_orders = {}
        self.active_positions = {}
    
    def connect(self) -> bool:
        """Connect to MT5 for trade execution."""
        try:
            if self.client.connect():
                self.is_connected = True

                # Connect balance manager
                if not self.balance_manager.connect():
                    self.logger.warning("Balance manager connection failed, will use fallback")

                # Verify symbol and trading permissions
                symbol_info = self.client.get_symbol_info(self.symbol)
                if not symbol_info:
                    raise MT5DataError(f"Symbol {self.symbol} not available for trading")

                # Get account info through balance manager
                account_info = self.balance_manager.get_account_info()
                if not account_info:
                    raise MT5DataError("Failed to get account information")

                self.logger.info(f"Account balance: ${account_info.balance:,.2f} ({account_info.source})")
                self.logger.info(f"Account equity: ${account_info.equity:,.2f}")
                self.logger.info(f"Free margin: ${account_info.margin_free:,.2f}")

                self.logger.info(f"Connected to MT5 for trade execution: {self.symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect for trade execution: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5."""
        if self.is_connected:
            self.client.disconnect()
            self.balance_manager.disconnect()
            self.is_connected = False
            self.logger.info("Disconnected from MT5 trade execution")

    def execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute trade with automatic account balance retrieval.

        This is the main interface method called by the trading system.
        It automatically retrieves current account balance and executes the trade.

        Args:
            signal: Trading signal from model engine

        Returns:
            Dictionary with execution result
        """
        try:
            # Get current price from signal or fetch from market
            current_price = signal.get('current_price')
            if not current_price:
                # Would need to fetch current price from data collector
                # For now, use a placeholder - this should be improved
                self.logger.warning("No current price in signal, using placeholder")
                current_price = 2000.0  # XAUUSD approximate price

            # Get current account balance
            account_info = self.balance_manager.get_account_info()
            account_balance = account_info.balance

            self.logger.info(f"Executing trade with balance: ${account_balance:,.2f} ({account_info.source})")

            # Execute the trade
            trade = self.execute_model_driven_trade(signal, current_price, account_balance)

            if trade:
                return {
                    'success': True,
                    'trade_id': trade.trade_id,
                    'entry_price': trade.entry_price,
                    'position_size': trade.position_size,
                    'message': f"Trade executed successfully: {trade.direction.value} {trade.position_size} lots"
                }
            else:
                return {
                    'success': False,
                    'error': 'Trade execution failed',
                    'message': 'Failed to execute trade - check logs for details'
                }

        except Exception as e:
            error_msg = f"Error in execute_trade: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'message': 'Trade execution failed due to system error'
            }

    def execute_model_driven_trade(self, signal: Dict[str, Any],
                                 current_price: float,
                                 account_balance: float) -> Optional[LiveTrade]:
        """
        Execute trade with model-driven optimization.
        
        Args:
            signal: Trading signal from model engine
            current_price: Current market price
            account_balance: Current account balance
            
        Returns:
            LiveTrade object if successful, None otherwise
        """
        if not self.is_connected:
            if not self.connect():
                return None
        
        # Check if we have a valid trading signal
        if not signal.get('direction') or signal.get('confidence', 0) < 0.5:
            self.logger.warning(f"Invalid signal: direction={signal.get('direction')}, confidence={signal.get('confidence', 0)}")
            return None
        
        try:
            start_time = time.time()
            
            # Calculate position size with model-driven adjustments
            position_size = self._calculate_position_size(
                signal, account_balance, current_price
            )
            
            if position_size <= 0:
                self.logger.warning("Position size calculation resulted in zero or negative size")
                return None
            
            # Determine optimal entry price and method
            entry_result = self._determine_optimal_entry(signal, current_price)
            
            # Create trade object
            trade = LiveTrade(
                trade_id=str(uuid.uuid4()),
                symbol=self.symbol,
                direction=TradeDirection.LONG if signal['direction'].lower() in ['long', 'buy'] else TradeDirection.SHORT,
                entry_time=datetime.now(),
                entry_price=round( entry_result['target_price'], 2),
                entry_type=entry_result['order_type'],
                position_size=position_size,
                model_confidence=signal.get('confidence', 0.0),
                model_consensus={},  # Could be populated with individual model scores
                signal_probability=signal.get('confidence', 0.0)
            )
            
            # Calculate model-driven TP/SL levels
            tp_sl_levels = self._calculate_model_driven_levels(signal, entry_result['target_price'])
            trade.tp1_price = round(tp_sl_levels['tp1_price'], 2) 
            trade.tp2_price = round(tp_sl_levels['tp2_price'], 2) 
            trade.tp3_price = round(tp_sl_levels['tp3_price'], 2) 
            trade.sl_price = round(tp_sl_levels['sl_price'], 2)
      
            # if tp_sl_levels['sl_price'] < trade.entry_price:
            #     distance = trade.entry_price - tp_sl_levels['sl_price']
            #     tp_sl_levels = self._calculate_model_driven_levels(signal, entry_result['target_price'])
            #     trade.tp1_price = tp_sl_levels['tp1_price'] + (9* distance)
            #     trade.tp2_price = tp_sl_levels['tp2_price'] + (9* distance)
            #     trade.tp3_price = tp_sl_levels['tp3_price'] + (9* distance)
            #     trade.sl_price = tp_sl_levels['sl_price'] - (9* distance)
            # else:
            #     distance = tp_sl_levels['sl_price'] - trade.entry_price 
            #     tp_sl_levels = self._calculate_model_driven_levels(signal, entry_result['target_price'])
            #     trade.tp1_price = tp_sl_levels['tp1_price'] - (9* distance)
            #     trade.tp2_price = tp_sl_levels['tp2_price'] - (9* distance)
            #     trade.tp3_price = tp_sl_levels['tp3_price'] - (9* distance)
            #     trade.sl_price = tp_sl_levels['sl_price'] + (9* distance)
            
            
            # Execute the trade
            execution_result = self._execute_mt5_order(trade, entry_result)
            
            if execution_result['success']:
                trade.status = TradeStatus.OPEN
                trade.entry_price = execution_result['fill_price']
                trade.spread_cost = execution_result['spread_cost']
                trade.slippage_cost = execution_result['slippage_cost']
                
                # Set up TP/SL orders
                self._setup_multi_tier_exits(trade)
                
                # Update statistics
                execution_time_ms = (time.time() - start_time) * 1000
                self._update_execution_stats(execution_result, execution_time_ms)
                
                self.logger.info(f"✓ Trade executed: {trade.trade_id} - "
                               f"{trade.direction.value} {trade.position_size} lots at {trade.entry_price}")
                
                return trade
            else:
                self.logger.error(f"Trade execution failed: {execution_result['error']}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error executing model-driven trade: {str(e)}")
            self.execution_stats['errors'] += 1
            return None
    
    def _calculate_position_size(self, signal: Dict[str, Any],
                               account_balance: float, current_price: float) -> float:
        """Calculate position size with model-driven adjustments and comprehensive logging."""

        # Log initial parameters
        self.logger.info("=== POSITION SIZE CALCULATION ===")
        self.logger.info(f"Account Balance: ${account_balance:,.2f}")

        # Base risk calculation
        risk_per_trade = self.config.get('max_risk_per_trade', 0.02)
        max_risk_amount = account_balance * risk_per_trade
        sl_distance = signal.get('sl_distance', 20.0)

        self.logger.info(f"Risk per trade: {risk_per_trade:.1%} = ${max_risk_amount:.2f}")
        self.logger.info(f"Stop loss distance: {sl_distance:.1f} pips")

        # Calculate base position size
        pip_value = 1.0  # For XAUUSD, approximately $1 per pip per 0.01 lot
        if sl_distance <= 0:
            self.logger.error("Invalid stop loss distance, using default 20 pips")
            sl_distance = 20.0

        base_position_size = max_risk_amount / (sl_distance * pip_value)
        self.logger.info(f"Base position size: ${max_risk_amount:.2f} ÷ ({sl_distance:.1f} × {pip_value}) = {base_position_size:.4f} lots")

        # CRITICAL: Extract model-driven multipliers with detailed logging
        self.logger.info("🔍 EXTRACTING MODEL MULTIPLIERS:")

        confidence_multiplier = None
        volatility_adjustment = None

        # Source 1: Direct signal values
        if 'position_size_multiplier' in signal:
            confidence_multiplier = signal['position_size_multiplier']
            self.logger.info(f"   ✅ Found position_size_multiplier in signal: {confidence_multiplier:.3f}")
        if 'volatility_adjustment' in signal:
            volatility_adjustment = signal['volatility_adjustment']
            self.logger.info(f"   ✅ Found volatility_adjustment in signal: {volatility_adjustment:.3f}")

        # Source 2: Check nested predictions structure
        if 'predictions' in signal:
            predictions = signal['predictions']
            if 'position_size_category' in predictions and confidence_multiplier is None:
                confidence_multiplier = predictions['position_size_category']
                self.logger.info(f"   ✅ Found position_size_category in predictions: {confidence_multiplier:.3f}")
            if 'volatility_adjustment' in predictions and volatility_adjustment is None:
                volatility_adjustment = predictions['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in predictions: {volatility_adjustment:.3f}")

        # Source 3: Check trading_signal structure
        if 'trading_signal' in signal:
            trading_signal = signal['trading_signal']
            if 'position_size_multiplier' in trading_signal and confidence_multiplier is None:
                confidence_multiplier = trading_signal['position_size_multiplier']
                self.logger.info(f"   ✅ Found position_size_multiplier in trading_signal: {confidence_multiplier:.3f}")
            if 'volatility_adjustment' in trading_signal and volatility_adjustment is None:
                volatility_adjustment = trading_signal['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in trading_signal: {volatility_adjustment:.3f}")

        # Apply defaults only if no model predictions found
        if confidence_multiplier is None:
            confidence_multiplier = 1.0
            self.logger.warning(f"   ❌ No model position_size_multiplier found, using default: {confidence_multiplier:.3f}")
        if volatility_adjustment is None:
            volatility_adjustment = 1.0
            self.logger.warning(f"   ❌ No model volatility_adjustment found, using default: {volatility_adjustment:.3f}")

        self.logger.info(f"📊 FINAL MULTIPLIERS:")
        self.logger.info(f"   Ensemble multiplier: {confidence_multiplier:.3f}")
        self.logger.info(f"   Volatility adjustment: {volatility_adjustment:.3f}")

        # Session-based adjustment
        current_hour = datetime.now().hour
        session_config = self.config.get('trading_sessions', {})
        session_multiplier = 1.0
        current_session = "unknown"

        for session_name, session_data in session_config.items():
            start_hour = session_data.get('start_hour', 0)
            end_hour = session_data.get('end_hour', 24)
            if start_hour <= current_hour < end_hour:
                session_multiplier = session_data.get('position_multiplier', 1.0)
                current_session = session_name
                break

        self.logger.info(f"Session: {current_session} (hour {current_hour}) multiplier: {session_multiplier:.3f}")

        # Final position size calculation
        final_position_size = (
            base_position_size *
            confidence_multiplier *
            volatility_adjustment *
            session_multiplier
        )

        self.logger.info(f"Pre-limits size: {base_position_size:.4f} × {confidence_multiplier:.3f} × {volatility_adjustment:.3f} × {session_multiplier:.3f} = {final_position_size:.4f} lots")

        # Apply limits
        min_lot = 0.01
        max_lot = self.config.get('max_position_size', 10.0)
        original_size = final_position_size
        final_position_size = max(min_lot, min(final_position_size, max_lot))

        if final_position_size != original_size:
            self.logger.info(f"Size adjusted by limits: {original_size:.4f} → {final_position_size:.4f} lots")

        # Round to valid lot size
        final_position_size = round(final_position_size, 2)

        # Final validation
        if final_position_size <= 0:
            self.logger.error("Final position size is zero or negative, using minimum")
            final_position_size = min_lot

        # Calculate final risk amount for verification
        final_risk_amount = final_position_size * sl_distance * pip_value
        risk_percentage = final_risk_amount / account_balance

        self.logger.info(f"FINAL POSITION SIZE: {final_position_size:.2f} lots")
        self.logger.info(f"Final risk amount: ${final_risk_amount:.2f} ({risk_percentage:.2%} of balance)")
        self.logger.info("=== END POSITION SIZE CALCULATION ===")

        return final_position_size
    
    def _determine_optimal_entry(self, signal: Dict[str, Any], 
                               current_price: float) -> Dict[str, Any]:
        """Determine optimal entry price and order type."""
        if not self.use_model_entry_timing:
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Model-driven entry optimization
        entry_urgency = signal.get('entry_urgency', 'normal')
        confidence = signal.get('confidence', 0.0)
        volatility_adjustment = signal.get('volatility_adjustment', 1.0)
        
        if entry_urgency == 'high' or confidence > 0.85:
            # High confidence - immediate market entry
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Calculate optimal entry offset
        direction = signal.get('direction', 'long')
        base_offset = 2.0 * volatility_adjustment  # Base offset in pips
        
        if direction == 'long':
            target_price = current_price - (base_offset * 0.01)  # Better entry for long
        else:
            target_price = current_price + (base_offset * 0.01)  # Better entry for short
        
        return {
            'target_price': target_price,
            'order_type': OrderType.LIMIT,
            'patience_seconds': self.entry_patience_seconds
        }
    
    def _calculate_model_driven_levels(self, signal: Dict[str, Any],
                                     entry_price: float) -> Dict[str, float]:
        """Calculate TP/SL levels using model predictions with MT5 validation and comprehensive logging."""

        self.logger.info("🎯 MODEL-DRIVEN TP/SL CALCULATION STARTING")
        self.logger.info("=" * 50)

        direction = signal.get('direction', 'long').lower()
        direction_multiplier = 1 if direction in ['long', 'buy'] else -1

        self.logger.info(f"Direction: {direction.upper()}, Entry Price: {entry_price:.2f}")

        # Get symbol info for stop level validation
        symbol_info = self.client.get_symbol_info(self.symbol)
        if not symbol_info:
            self.logger.warning(f"Could not get symbol info for {self.symbol}, using default stops")
            min_stop_distance = 10.0  # Default minimum distance in pips
        else:
            # MT5 stops_level is in points, convert to pips
            min_stop_distance = max(symbol_info.get('stops_level', 10) / 10.0, 5.0)
            self.logger.info(f"MT5 minimum stop distance for {self.symbol}: {min_stop_distance} pips")

        # CRITICAL: Extract model predictions with detailed logging
        self.logger.info("🔍 EXTRACTING MODEL PREDICTIONS:")

        # Check multiple possible sources for model predictions
        tp1_distance = None
        tp2_distance = None
        tp3_distance = None
        sl_distance = None
        vol_adjustment = None

        # Source 1: Direct signal values
        if 'tp1_distance' in signal:
            tp1_distance = signal['tp1_distance']
            self.logger.info(f"   ✅ Found tp1_distance in signal: {tp1_distance:.1f}")
        if 'tp2_distance' in signal:
            tp2_distance = signal['tp2_distance']
            self.logger.info(f"   ✅ Found tp2_distance in signal: {tp2_distance:.1f}")
        if 'tp3_distance' in signal:
            tp3_distance = signal['tp3_distance']
            self.logger.info(f"   ✅ Found tp3_distance in signal: {tp3_distance:.1f}")
        if 'sl_distance' in signal:
            sl_distance = signal['sl_distance']
            self.logger.info(f"   ✅ Found sl_distance in signal: {sl_distance:.1f}")
        if 'volatility_adjustment' in signal:
            vol_adjustment = signal['volatility_adjustment']
            self.logger.info(f"   ✅ Found volatility_adjustment in signal: {vol_adjustment:.2f}")

        # Source 2: Check nested predictions structure
        if 'predictions' in signal:
            predictions = signal['predictions']
            if 'tp1_distance' in predictions and tp1_distance is None:
                tp1_distance = predictions['tp1_distance']
                self.logger.info(f"   ✅ Found tp1_distance in predictions: {tp1_distance:.1f}")
            if 'tp2_distance' in predictions and tp2_distance is None:
                tp2_distance = predictions['tp2_distance']
                self.logger.info(f"   ✅ Found tp2_distance in predictions: {tp2_distance:.1f}")
            if 'sl_distance' in predictions and sl_distance is None:
                sl_distance = predictions['sl_distance']
                self.logger.info(f"   ✅ Found sl_distance in predictions: {sl_distance:.1f}")
            if 'volatility_adjustment' in predictions and vol_adjustment is None:
                vol_adjustment = predictions['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in predictions: {vol_adjustment:.2f}")

        # Source 3: Check trading_signal structure
        if 'trading_signal' in signal:
            trading_signal = signal['trading_signal']
            if 'tp1_distance' in trading_signal and tp1_distance is None:
                tp1_distance = trading_signal['tp1_distance']
                self.logger.info(f"   ✅ Found tp1_distance in trading_signal: {tp1_distance:.1f}")
            if 'tp2_distance' in trading_signal and tp2_distance is None:
                tp2_distance = trading_signal['tp2_distance']
                self.logger.info(f"   ✅ Found tp2_distance in trading_signal: {tp2_distance:.1f}")
            if 'sl_distance' in trading_signal and sl_distance is None:
                sl_distance = trading_signal['sl_distance']
                self.logger.info(f"   ✅ Found sl_distance in trading_signal: {sl_distance:.1f}")
            if 'volatility_adjustment' in trading_signal and vol_adjustment is None:
                vol_adjustment = trading_signal['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in trading_signal: {vol_adjustment:.2f}")

        # Apply defaults only if no model predictions found
        current_atr = signal.get('current_atr', 40.0)  # Default ~40 pips ATR for XAUUSD

        if tp1_distance is None:
            tp1_distance = current_atr * 1.5   # 1.5x ATR
            self.logger.warning(f"   ❌ No model tp1_distance found, using ATR-based: {tp1_distance:.1f}")
        if tp2_distance is None:
            tp2_distance = current_atr * 3.0   # 3.0x ATR
            self.logger.warning(f"   ❌ No model tp2_distance found, using ATR-based: {tp2_distance:.1f}")
        if tp3_distance is None:
            tp3_distance = current_atr * 5.0   # 5.0x ATR
            self.logger.warning(f"   ❌ No model tp3_distance found, using ATR-based: {tp3_distance:.1f}")
        if sl_distance is None:
            sl_distance = current_atr * 2.0    # 2.0x ATR
            self.logger.warning(f"   ❌ No model sl_distance found, using ATR-based: {sl_distance:.1f}")
        if vol_adjustment is None:
            vol_adjustment = 1.0
            self.logger.warning(f"   ❌ No model volatility_adjustment found, using default: {vol_adjustment:.2f}")

        # Apply volatility adjustment
        original_tp1 = tp1_distance
        original_tp2 = tp2_distance
        original_tp3 = tp3_distance
        original_sl = sl_distance

        tp1_distance *= vol_adjustment
        tp2_distance *= vol_adjustment
        tp3_distance *= vol_adjustment
        sl_distance *= vol_adjustment

        if vol_adjustment != 1.0:
            self.logger.info(f"📊 VOLATILITY ADJUSTMENT APPLIED ({vol_adjustment:.2f}x):")
            self.logger.info(f"   TP1: {original_tp1:.1f} → {tp1_distance:.1f}")
            self.logger.info(f"   TP2: {original_tp2:.1f} → {tp2_distance:.1f}")
            self.logger.info(f"   TP3: {original_tp3:.1f} → {tp3_distance:.1f}")
            self.logger.info(f"   SL:  {original_sl:.1f} → {sl_distance:.1f}")

        # Validate minimum distances (but use realistic minimums for XAUUSD)
        xauusd_min_distance = max(min_stop_distance, 20.0)  # At least 20 pips for XAUUSD
        tp1_distance = max(tp1_distance, xauusd_min_distance)
        tp2_distance = max(tp2_distance, xauusd_min_distance * 1.5)  # At least 30 pips
        tp3_distance = max(tp3_distance, xauusd_min_distance * 2.0)  # At least 40 pips
        sl_distance = max(sl_distance, xauusd_min_distance)

        # Calculate actual price levels
        tp1_price = entry_price + (direction_multiplier * tp1_distance * 0.01)
        tp2_price = entry_price + (direction_multiplier * tp2_distance * 0.01)
        tp3_price = entry_price + (direction_multiplier * tp3_distance * 0.01)
        sl_price = entry_price - (direction_multiplier * sl_distance * 0.01)

        # Final logging
        self.logger.info(f"🎯 FINAL CALCULATED LEVELS for {direction.upper()} at {entry_price:.2f}:")
        self.logger.info(f"   TP1: {tp1_price:.2f} ({tp1_distance:.1f} pips)")
        self.logger.info(f"   TP2: {tp2_price:.2f} ({tp2_distance:.1f} pips)")
        self.logger.info(f"   TP3: {tp3_price:.2f} ({tp3_distance:.1f} pips)")
        self.logger.info(f"   SL:  {sl_price:.2f} ({sl_distance:.1f} pips)")
        self.logger.info("=" * 50)

        return {
            'tp1_price': tp1_price,
            'tp2_price': tp2_price,
            'tp3_price': tp3_price,
            'sl_price': sl_price,
            'tp1_distance': tp1_distance,
            'tp2_distance': tp2_distance,
            'tp3_distance': tp3_distance,
            'sl_distance': sl_distance
        }

    def _execute_mt5_order(self, trade: LiveTrade, entry_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute market order with MT5 at the last acceptable price (no pending orders)."""
        try:
            # Get symbol information for price normalization
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                return {'success': False, 'error': f'Symbol {self.symbol} not found'}
            
            # Check if trading is allowed
            if not symbol_info.trade_mode:
                return {'success': False, 'error': 'Trading is disabled for this symbol'}
            
            # Get current tick data
            tick = mt5.symbol_info_tick(self.symbol)
            if tick is None:
                return {'success': False, 'error': 'Cannot get current tick data'}
            
            # Helper function to normalize prices
            def normalize_price(price):
                """Normalize price to symbol's tick size and digits"""
                digits = symbol_info.digits
                price = round(price, digits)
                tick_size = symbol_info.trade_tick_size
                if tick_size > 0:
                    price = round(price / tick_size) * tick_size
                    price = round(price, digits)
                return price
            
            # Always use current market price for execution
            if trade.direction == TradeDirection.LONG:
                entry_price = normalize_price(tick.ask)
            else:
                entry_price = normalize_price(tick.bid)
            
            # Normalize SL and TP prices
            sl_price = normalize_price(trade.sl_price)
            tp_price = normalize_price(trade.tp3_price)
            
            # Check minimum distance requirements
            min_stop_level = symbol_info.trade_stops_level
            point = symbol_info.point
            min_distance = min_stop_level * point
            
            # Validate and adjust stop levels
            sl_distance = abs(entry_price - sl_price)
            tp_distance = abs(entry_price - tp_price)
            
            if sl_distance < min_distance:
                self.logger.warning(f"SL distance {sl_distance} < minimum {min_distance}, adjusting...")
                if trade.direction == TradeDirection.LONG:
                    sl_price = normalize_price(entry_price - min_distance)
                else:
                    sl_price = normalize_price(entry_price + min_distance)
            
            if tp_distance < min_distance:
                self.logger.warning(f"TP distance {tp_distance} < minimum {min_distance}, adjusting...")
                if trade.direction == TradeDirection.LONG:
                    tp_price = normalize_price(entry_price + min_distance)
                else:
                    tp_price = normalize_price(entry_price - min_distance)
            
            # Final validation of stop levels
            if trade.direction == TradeDirection.LONG:
                if sl_price >= entry_price:
                    self.logger.error(f"Invalid SL for LONG: SL {sl_price} >= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid stop loss level for LONG position'}
                if tp_price <= entry_price:
                    self.logger.error(f"Invalid TP for LONG: TP {tp_price} <= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid take profit level for LONG position'}
            else:  # SHORT
                if sl_price <= entry_price:
                    self.logger.error(f"Invalid SL for SHORT: SL {sl_price} <= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid stop loss level for SHORT position'}
                if tp_price >= entry_price:
                    self.logger.error(f"Invalid TP for SHORT: TP {tp_price} >= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid take profit level for SHORT position'}

            # Log the normalized levels for debugging
            self.logger.info(f"Normalized order levels - Entry: {entry_price}, SL: {sl_price}, TP: {tp_price}")
            self.logger.info(f"Symbol info - Digits: {symbol_info.digits}, Tick size: {symbol_info.trade_tick_size}, Min stop level: {min_stop_level}")

            # Always send MARKET order
            if trade.direction == TradeDirection.LONG:
                order_type = mt5.ORDER_TYPE_BUY
                action = mt5.TRADE_ACTION_DEAL
                price = entry_price
            else:
                order_type = mt5.ORDER_TYPE_SELL
                action = mt5.TRADE_ACTION_DEAL
                price = entry_price

            # Prepare order request
            request = {
                "action": action,
                "symbol": self.symbol,
                "volume": float(trade.position_size),
                "type": order_type,
                "price": price,
                "sl": sl_price,
                "tp": tp_price,
                "deviation": int(self.max_slippage_pips * 10),  # allow some slippage
                "magic": 12345,
                "comment": f"AI_Trade_{trade.trade_id[:8]}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,  # Immediate-Or-Cancel for market orders
            }
            
            # Log the request for debugging
            self.logger.info(f"Sending order request: {request}")
            
            # Check number of open positions before sending
            open_positions = mt5.positions_get(symbol=self.symbol)
            if open_positions is not None and len(open_positions) >= 3:
                self.logger.warning(f"Max open trades reached (3). No new trade will be sent for {self.symbol}.")
                return {
                    'success': False,
                    'error': 'Maximum open trades limit reached'
                }

            # Send order
            result = mt5.order_send(request)
            
            if result is None:
                last_error = mt5.last_error()
                return {
                    'success': False,
                    'error': f"Order send failed: {last_error}"
                }
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                # Log more details about the rejection
                self.logger.error(f"Order rejected - Code: {result.retcode}, Comment: {result.comment}")
                self.logger.error(f"Request was: {request}")
                return {
                    'success': False,
                    'error': f"Order rejected: {result.retcode} - {result.comment}"
                }
            
            # Calculate costs
            fill_price = result.price
            spread_cost = self.spread_pips * trade.position_size * 1.0
            slippage_cost = abs(fill_price - price) * trade.position_size * 100
            
            self.logger.info(f"Order executed successfully - Ticket: {result.order}, Fill price: {fill_price}")
            
            return {
                'success': True,
                'order_ticket': result.order,
                'fill_price': fill_price,
                'spread_cost': spread_cost,
                'slippage_cost': slippage_cost,
                'commission': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"MT5 execution error: {str(e)}")
            return {
                'success': False,
                'error': f"MT5 execution error: {str(e)}"
            }

    def _setup_multi_tier_exits(self, trade: LiveTrade):
        """
        Set up multi-tier TP/SL system using partial close approach.

        Strategy:
        - Single position with one SL for entire position
        - Partial closes at TP1 (40%), TP2 (35%), TP3 (25% trailing)
        - Track partial close levels in our system
        """
        try:
            self.logger.info(f"Setting up multi-tier exits for trade {trade.trade_id}")

            # Store multi-tier configuration in active positions
            self.active_positions[trade.trade_id] = {
                'trade': trade,
                'original_size': trade.position_size,
                'remaining_size': trade.position_size,
                'tp1_executed': False,
                'tp2_executed': False,
                'tp3_active': False,
                'tp1_size': trade.position_size * self.tp1_percentage,  # 40%
                'tp2_size': trade.position_size * self.tp2_percentage,  # 35%
                'tp3_size': trade.position_size * self.tp3_percentage,  # 25%
                'entry_time': datetime.now(),
                'last_check': datetime.now()
            }

            self.logger.info(f"Multi-tier plan: TP1@{trade.tp1_price:.2f} ({self.tp1_percentage:.0%}), "
                           f"TP2@{trade.tp2_price:.2f} ({self.tp2_percentage:.0%}), "
                           f"TP3@{trade.tp3_price:.2f} ({self.tp3_percentage:.0%})")

        except Exception as e:
            self.logger.error(f"Failed to setup multi-tier exits: {str(e)}")

    def check_multi_tier_exits(self):
        """
        Check and execute multi-tier exits for active positions.
        Should be called regularly (every 30-60 seconds) by the trading loop.
        """
        if not self.active_positions:
            return

        current_price = self._get_current_price()
        if current_price is None:
            return

        positions_to_remove = []

        for trade_id, position_info in self.active_positions.items():
            try:
                trade = position_info['trade']

                # Check if position still exists in MT5
                if not self._position_exists(trade_id):
                    positions_to_remove.append(trade_id)
                    continue

                # Check TP1 (40% partial close)
                if not position_info['tp1_executed']:
                    if self._price_hit_target(current_price, trade.tp1_price, trade.direction):
                        self._execute_partial_close(trade_id, position_info['tp1_size'], "TP1")
                        position_info['tp1_executed'] = True
                        self.logger.info(f"✓ TP1 hit for {trade_id}: Closed {position_info['tp1_size']:.2f} lots at {current_price:.2f}")

                # Check TP2 (35% partial close)
                if position_info['tp1_executed'] and not position_info['tp2_executed']:
                    if self._price_hit_target(current_price, trade.tp2_price, trade.direction):
                        self._execute_partial_close(trade_id, position_info['tp2_size'], "TP2")
                        position_info['tp2_executed'] = True
                        position_info['tp3_active'] = True  # Activate trailing for remaining 25%
                        self.logger.info(f"✓ TP2 hit for {trade_id}: Closed {position_info['tp2_size']:.2f} lots at {current_price:.2f}")
                        self.logger.info(f"🎯 TP3 trailing activated for remaining {position_info['tp3_size']:.2f} lots")

                # Check TP3 (25% trailing or target)
                if position_info['tp2_executed'] and position_info['tp3_active']:
                    if self._price_hit_target(current_price, trade.tp3_price, trade.direction):
                        self._execute_partial_close(trade_id, position_info['tp3_size'], "TP3")
                        positions_to_remove.append(trade_id)  # All TPs hit, remove from tracking
                        self.logger.info(f"✓ TP3 hit for {trade_id}: Closed final {position_info['tp3_size']:.2f} lots at {current_price:.2f}")

                position_info['last_check'] = datetime.now()

            except Exception as e:
                self.logger.error(f"Error checking multi-tier exits for {trade_id}: {str(e)}")

        # Clean up completed positions
        for trade_id in positions_to_remove:
            del self.active_positions[trade_id]

    def _get_current_price(self) -> Optional[float]:
        """Get current market price for the symbol."""
        try:
            tick = mt5.symbol_info_tick(self.symbol)
            if tick:
                return (tick.bid + tick.ask) / 2  # Mid price
            return None
        except Exception as e:
            self.logger.error(f"Failed to get current price: {str(e)}")
            return None

    def _position_exists(self, trade_id: str) -> bool:
        """Check if position still exists in MT5."""
        try:
            positions = mt5.positions_get(symbol=self.symbol)
            if positions:
                for pos in positions:
                    if str(pos.comment).endswith(trade_id[:8]):  # Match trade ID
                        return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to check position existence: {str(e)}")
            return False

    def _price_hit_target(self, current_price: float, target_price: float, direction: 'TradeDirection') -> bool:
        """Check if current price has hit the target level."""
        if direction.value.lower() in ['long', 'buy']:
            return current_price >= target_price
        else:
            return current_price <= target_price

    def _execute_partial_close(self, trade_id: str, close_size: float, level_name: str):
        """Execute partial position close."""
        try:
            # Find the MT5 position
            positions = mt5.positions_get(symbol=self.symbol)
            target_position = None

            if positions:
                for pos in positions:
                    if str(pos.comment).endswith(trade_id[:8]):
                        target_position = pos
                        break

            if not target_position:
                self.logger.error(f"Position not found for partial close: {trade_id}")
                return False

            # Prepare close request
            close_type = mt5.ORDER_TYPE_SELL if target_position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": close_size,
                "type": close_type,
                "position": target_position.ticket,
                "deviation": int(self.max_slippage_pips * 10),
                "magic": 12345,
                "comment": f"Partial_Close_{level_name}_{trade_id[:8]}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Execute partial close
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info(f"✓ Partial close executed: {level_name} - {close_size:.2f} lots at {result.price:.2f}")
                return True
            else:
                self.logger.error(f"Partial close failed: {result.retcode if result else 'No result'}")
                return False

        except Exception as e:
            self.logger.error(f"Error executing partial close: {str(e)}")
            return False

    def _update_execution_stats(self, execution_result: Dict[str, Any], execution_time_ms: float):
        """Update execution statistics."""
        self.execution_stats['orders_placed'] += 1
        
        if execution_result['success']:
            self.execution_stats['orders_filled'] += 1
            
            # Update averages
            total_filled = self.execution_stats['orders_filled']
            
            self.execution_stats['avg_execution_time_ms'] = (
                (self.execution_stats['avg_execution_time_ms'] * (total_filled - 1) + execution_time_ms) / total_filled
            )
            
            slippage_pips = execution_result.get('slippage_cost', 0.0) / 100  # Convert to pips
            self.execution_stats['avg_slippage_pips'] = (
                (self.execution_stats['avg_slippage_pips'] * (total_filled - 1) + slippage_pips) / total_filled
            )
            
            self.execution_stats['total_spread_cost'] += execution_result.get('spread_cost', 0.0)
            
        else:
            self.execution_stats['orders_rejected'] += 1
        
        self.execution_stats['last_execution_time'] = datetime.now()
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution performance statistics."""
        total_orders = self.execution_stats['orders_placed']
        fill_rate = self.execution_stats['orders_filled'] / max(total_orders, 1)
        
        return {
            **self.execution_stats,
            'fill_rate': fill_rate,
            'rejection_rate': self.execution_stats['orders_rejected'] / max(total_orders, 1),
            'is_connected': self.is_connected,
            'pending_orders_count': len(self.pending_orders),
            'active_positions_count': len(self.active_positions)
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
