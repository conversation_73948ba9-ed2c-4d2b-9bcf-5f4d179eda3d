"""
Hierarchical Decision Framework for Specialized Ensemble Architecture

Implements 4-level decision process with veto power at each level:
Level 1: Safety Check (Linear Stability Monitor)
Level 2: Market Context Analysis (CatBoost Market Regime Analyst)  
Level 3: Signal Quality Assessment (LightGBM Signal Generator)
Level 4: Final Decision Integration (XGBoost Risk Manager)
"""

from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import numpy as np
import pandas as pd
try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    import logging
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)


@dataclass
class DecisionLevel:
    """Represents a decision level in the hierarchy."""
    level: int
    name: str
    model_type: str
    weight: float
    veto_power: bool
    decision: Optional[str] = None
    confidence: float = 0.0
    reasoning: str = ""
    veto_reason: Optional[str] = None


@dataclass
class HierarchicalDecision:
    """Complete hierarchical decision result."""
    final_decision: str  # 'BUY', 'SELL', 'HOLD', 'VETO'
    overall_confidence: float
    decision_levels: Dict[int, DecisionLevel]
    consensus_score: float
    risk_score: float
    entry_timing: str  # 'IMMEDIATE', 'PATIENT', 'WAIT'
    position_sizing: float
    tp_sl_levels: Dict[str, float]
    timestamp: datetime


class HierarchicalDecisionFramework(LoggerMixin):
    """
    Implements the 4-level hierarchical decision framework for specialized ensemble.
    
    Each level has veto power over subsequent levels, ensuring comprehensive
    risk management and decision quality control.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        # Override logger name to match other components
        import logging
        self.logger = logging.getLogger("HierarchicalDecision")
        self.config = config
        
        # Decision level configuration
        self.decision_levels = {
            1: DecisionLevel(
                level=1,
                name="Safety Check",
                model_type="linear",
                weight=0.00,  # 🔧 DISABLED: Set to zero to bypass low liquidity hours veto
                veto_power=False  # 🔧 DISABLED: Remove veto power to bypass low liquidity hours veto
            ),
            2: DecisionLevel(
                level=2,
                name="Market Context Analysis", 
                model_type="catboost",
                weight=0.25,
                veto_power=True
            ),
            3: DecisionLevel(
                level=3,
                name="Signal Quality Assessment",
                model_type="lightgbm", 
                weight=0.40,
                veto_power=True
            ),
            4: DecisionLevel(
                level=4,
                name="Final Decision Integration",
                model_type="xgboost",
                weight=0.25,
                veto_power=False  # Final level doesn't veto, just adjusts
            )
        }
        
        # Thresholds from config - adjusted for realistic trading
        self.min_confidence_threshold = config.get('min_signal_confidence', 0.50)  # Lowered from 0.65
        self.high_confidence_threshold = config.get('high_confidence_threshold', 0.75)  # Lowered from 0.8
        self.consensus_threshold = config.get('ensemble_consensus_threshold', 0.60)  # Lowered from 0.7
        
        # Decision history for learning
        self.decision_history = []
        
        self.logger.info("Hierarchical Decision Framework initialized")
    
    def make_decision(self, model_predictions: Dict[str, Any],
                     market_context: Dict[str, Any]) -> HierarchicalDecision:
        """
        Make hierarchical decision through 4-level process.

        Args:
            model_predictions: Predictions from all specialized models
            market_context: Current market context and conditions

        Returns:
            HierarchicalDecision with complete decision analysis
        """
        # Professional logging for hierarchical decision analysis
        self.logger.info("🚀 HIERARCHICAL DECISION ANALYSIS INITIATED")
        self.logger.info(f"   Model predictions: {len(model_predictions)} models")
        self.logger.info(f"   Market context: {market_context.get('current_price', 'N/A')} | Regime: {market_context.get('market_regime', 'N/A')}")

        try:
            timestamp = datetime.now()
            decision_levels = {}

            # Log initial model predictions with enhanced detail
            self.logger.info("🔍 HIERARCHICAL DECISION ANALYSIS STARTING")
            self.logger.info("=" * 60)

            # Log individual model outputs with key metrics
            for model_name, prediction in model_predictions.items():
                if isinstance(prediction, dict):
                    confidence = prediction.get('confidence', 0.0)
                    outputs = prediction.get('outputs', {})

                    # Also check for direct prediction values (from LiveModelEngine)
                    predictions_dict = prediction.get('predictions', {})
                    trading_signal = prediction.get('trading_signal', {})

                    key_metrics = []

                    # Extract key metrics from outputs
                    if 'signal_strength' in outputs:
                        key_metrics.append(f"signal_strength={outputs['signal_strength']:.3f}")
                    if 'confidence_score' in outputs:
                        key_metrics.append(f"confidence_score={outputs['confidence_score']:.3f}")
                    if 'direction_classification' in outputs:
                        key_metrics.append(f"direction={outputs['direction_classification']}")
                    if 'signal_probability' in outputs:
                        key_metrics.append(f"signal_prob={outputs['signal_probability']:.3f}")
                    if 'risk_classification' in outputs:
                        key_metrics.append(f"risk={outputs['risk_classification']}")
                    if 'market_regime' in outputs:
                        key_metrics.append(f"regime={outputs['market_regime']}")
                    if 'session_favorability' in outputs:
                        key_metrics.append(f"session={outputs['session_favorability']}")
                    if 'volatility_state' in outputs:
                        key_metrics.append(f"volatility={outputs['volatility_state']}")
                    if 'baseline_signal' in outputs:
                        key_metrics.append(f"baseline={outputs['baseline_signal']}")
                    if 'system_health_score' in outputs:
                        key_metrics.append(f"health={outputs['system_health_score']:.3f}")

                    # Extract key metrics from predictions (LiveModelEngine format)
                    if 'signal_probability' in predictions_dict:
                        key_metrics.append(f"signal_prob={predictions_dict['signal_probability']:.3f}")
                    if 'tp1_distance' in predictions_dict:
                        key_metrics.append(f"tp1={predictions_dict['tp1_distance']:.1f}")
                    if 'tp2_distance' in predictions_dict:
                        key_metrics.append(f"tp2={predictions_dict['tp2_distance']:.1f}")
                    if 'sl_distance' in predictions_dict:
                        key_metrics.append(f"sl={predictions_dict['sl_distance']:.1f}")
                    if 'volatility_adjustment' in predictions_dict:
                        key_metrics.append(f"vol_adj={predictions_dict['volatility_adjustment']:.2f}")
                    if 'market_regime' in predictions_dict:
                        key_metrics.append(f"regime={predictions_dict['market_regime']:.3f}")

                    # Extract from trading signal
                    if 'direction' in trading_signal:
                        key_metrics.append(f"direction={trading_signal['direction']}")
                    if 'confidence' in trading_signal:
                        key_metrics.append(f"signal_conf={trading_signal['confidence']:.3f}")

                    metrics_str = ", ".join(key_metrics) if key_metrics else "no key metrics"
                    self.logger.info(f"📊 {model_name.upper()}: confidence={confidence:.3f} | {metrics_str}")

                    # Log the full prediction structure for debugging
                    if predictions_dict:
                        self.logger.info(f"   🔧 Full predictions: {predictions_dict}")
                    if trading_signal:
                        self.logger.info(f"   🎯 Trading signal: {trading_signal}")

                else:
                    self.logger.info(f"📊 {model_name.upper()}: {prediction}")

            self.logger.info("=" * 60)

            # Level 1: Safety Check (Linear Stability Monitor)
            self.logger.info("🛡️  LEVEL 1: SAFETY CHECK (Linear Stability Monitor)")
            level1_result = self._level1_safety_check(
                model_predictions.get('linear', {}),
                market_context
            )
            decision_levels[1] = level1_result

            self.logger.info(f"   Result: {level1_result.decision} | Confidence: {level1_result.confidence:.3f}")
            if level1_result.reasoning:
                self.logger.info(f"   Reasoning: {level1_result.reasoning}")

            if level1_result.veto_reason:
                self.logger.info(f"❌ LEVEL 1 VETO: {level1_result.veto_reason}")
                return self._create_veto_decision(level1_result, decision_levels, timestamp)

            # Level 2: Market Context Analysis (CatBoost Market Regime Analyst)
            self.logger.info("🌍 LEVEL 2: MARKET CONTEXT ANALYSIS (CatBoost Market Regime Analyst)")
            level2_result = self._level2_market_context(
                model_predictions.get('catboost', {}),
                market_context,
                level1_result
            )
            decision_levels[2] = level2_result

            self.logger.info(f"   Result: {level2_result.decision} | Confidence: {level2_result.confidence:.3f}")
            if level2_result.reasoning:
                self.logger.info(f"   Reasoning: {level2_result.reasoning}")

            if level2_result.veto_reason:
                self.logger.info(f"❌ LEVEL 2 VETO: {level2_result.veto_reason}")
                return self._create_veto_decision(level2_result, decision_levels, timestamp)

            # Level 3: Signal Quality Assessment (LightGBM Signal Generator)
            self.logger.info("📡 LEVEL 3: SIGNAL QUALITY ASSESSMENT (LightGBM Signal Generator)")
            level3_result = self._level3_signal_quality(
                model_predictions.get('lightgbm', {}),
                market_context,
                level1_result,
                level2_result
            )
            decision_levels[3] = level3_result

            self.logger.info(f"   Result: {level3_result.decision} | Confidence: {level3_result.confidence:.3f}")
            if level3_result.reasoning:
                self.logger.info(f"   Reasoning: {level3_result.reasoning}")

            if level3_result.veto_reason:
                self.logger.info(f"❌ LEVEL 3 VETO: {level3_result.veto_reason}")
                return self._create_veto_decision(level3_result, decision_levels, timestamp)
            
            # Level 4: Final Decision Integration (XGBoost Risk Manager)
            self.logger.info("⚖️  LEVEL 4: FINAL DECISION INTEGRATION (XGBoost Risk Manager)")
            level4_result = self._level4_final_integration(
                model_predictions.get('xgboost', {}),
                market_context,
                level1_result,
                level2_result,
                level3_result
            )
            decision_levels[4] = level4_result

            self.logger.info(f"   Result: {level4_result.decision} | Confidence: {level4_result.confidence:.3f}")
            if level4_result.reasoning:
                self.logger.info(f"   Reasoning: {level4_result.reasoning}")
            
            # Create final hierarchical decision
            final_decision = self._integrate_decisions(decision_levels, model_predictions, market_context)
            final_decision.timestamp = timestamp
            
            # Store in history
            self.decision_history.append(final_decision)
            
            self.logger.info(f"Hierarchical decision: {final_decision.final_decision} "
                           f"(confidence: {final_decision.overall_confidence:.3f}, "
                           f"consensus: {final_decision.consensus_score:.3f})")
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"🚨 HIERARCHICAL DECISION EXCEPTION: {str(e)}")
            import traceback
            self.logger.error(f"🚨 HIERARCHICAL DECISION TRACEBACK: {traceback.format_exc()}")
            return self._create_error_decision(str(e), timestamp)
    
    def _level1_safety_check(self, linear_pred: Dict[str, Any], 
                           market_context: Dict[str, Any]) -> DecisionLevel:
        """
        Level 1: Safety Check using Linear Stability Monitor.
        
        Checks for emergency conditions, system health, and basic risk limits.
        """
        level = DecisionLevel(
            level=1,
            name="Safety Check",
            model_type="linear",
            weight=0.00,  # 🔧 DISABLED: Set to zero to bypass low liquidity hours veto
            veto_power=False  # 🔧 DISABLED: Remove veto power to bypass low liquidity hours veto
        )
        
        try:
            # Extract linear model outputs
            if isinstance(linear_pred, dict) and 'outputs' in linear_pred:
                outputs = linear_pred['outputs']
                signal_prob = outputs.get('signal_probability', 0.5)
                volatility_adj = outputs.get('volatility_adjustment', 1.0)
                stability_score = outputs.get('stability_score', 0.5)
                risk_level = outputs.get('risk_classification', 'MEDIUM')
            else:
                signal_prob = linear_pred.get('signal_probability', 0.5)
                volatility_adj = linear_pred.get('volatility_adjustment', 1.0)
                stability_score = 0.5
                risk_level = 'MEDIUM'

            self.logger.info(f"     Linear Model Inputs: signal_prob={signal_prob:.3f}, "
                           f"volatility_adj={volatility_adj:.3f}, stability_score={stability_score:.3f}, "
                           f"risk_level={risk_level}")

            # Safety checks - only if veto power is enabled
            veto_reasons = []

            if level.veto_power:  # 🔧 Only perform safety checks if veto power is enabled
                # Check extreme volatility
                if volatility_adj > 2.5:
                    veto_reasons.append("Extreme volatility detected")

                # Check market hours (basic safety) - use UTC time
                import pytz
                current_utc_hour = datetime.now(pytz.UTC).hour
                if current_utc_hour < 1 or current_utc_hour > 23:  # Avoid very low liquidity hours (UTC)
                    veto_reasons.append("Low liquidity hours")

                # Check system health indicators
                if market_context.get('data_quality_score', 1.0) < 0.8:
                    veto_reasons.append("Poor data quality")

                # Check for emergency stops
                if market_context.get('emergency_stop_triggered', False):
                    veto_reasons.append("Emergency stop active")
            
            # Determine decision
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                level.confidence = 1.0
                level.reasoning = f"Safety veto: {level.veto_reason}"
            else:
                level.decision = "PROCEED"
                level.confidence = 1.0
                level.reasoning = f"Safety check passed (vol_adj: {volatility_adj:.2f})"
            
            return level

        except Exception as e:
            level.decision = "VETO"
            level.veto_reason = f"Safety check error: {str(e)}"
            level.confidence = 0.0
            level.reasoning = level.veto_reason
            return level

    def _level2_market_context(self, catboost_pred: Dict[str, Any],
                              market_context: Dict[str, Any],
                              level1: DecisionLevel) -> DecisionLevel:
        """
        Level 2: Market Context Analysis using CatBoost Market Regime Analyst.

        Analyzes market regime, volatility conditions, and trading session context.
        """
        level = DecisionLevel(
            level=2,
            name="Market Context Analysis",
            model_type="catboost",
            weight=0.25,
            veto_power=True
        )

        try:
            # Extract CatBoost regime analysis
            market_regime = catboost_pred.get('market_regime', 1.0)
            regime_confidence = catboost_pred.get('regime_confidence', 0.5)

            # Market context checks
            veto_reasons = []

            # Check market regime suitability
            if market_regime < 0.3:  # Very bearish/uncertain regime
                if level1.confidence < 0.7:  # Only proceed with high Level 1 confidence
                    veto_reasons.append("Unfavorable market regime with low safety confidence")

            # Check session-based trading rules (use UTC time for proper session detection)
            import pytz
            current_utc = datetime.now(pytz.UTC)
            current_local = datetime.now()
            current_hour = current_utc.hour
            session_multiplier = 1.0

            # 🔧 ENHANCED DEBUG LOGGING - Always log time comparison
            print(f"🕐 DEBUG TIME COMPARISON:")
            print(f"   Local time: {current_local.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   UTC time: {current_utc.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   UTC hour: {current_hour}")

            # Determine current session based on UTC time
            if 0 <= current_hour < 8:  # Asian session (00:00-08:00 UTC)
                current_session = "Asian"
                session_multiplier = market_context.get('asian_session_multiplier', 0.9)
                print(f"   🌏 Detected session: {current_session} (UTC hour {current_hour})")
                if regime_confidence < 0.6:
                    print(f"   ❌ VETO: regime_confidence ({regime_confidence}) < 0.6 during Asian session")
                    veto_reasons.append("Low regime confidence during Asian session")
            elif 8 <= current_hour < 16:  # European session (08:00-16:00 UTC)
                current_session = "European"
                session_multiplier = market_context.get('european_session_multiplier', 1.0)
                print(f"   🇪🇺 Detected session: {current_session} (UTC hour {current_hour})")
            else:  # US session (16:00-24:00 UTC)
                current_session = "US"
                session_multiplier = market_context.get('us_session_multiplier', 1.3)
                print(f"   🇺🇸 Detected session: {current_session} (UTC hour {current_hour})")

            # Log current session for debugging
            self.logger.warning(f"🕐 HIERARCHICAL DECISION: UTC={current_utc.strftime('%H:%M:%S')}, Session={current_session}, RegimeConf={regime_confidence}")

            # Check volatility regime compatibility
            vol_regime = market_context.get('volatility_regime', 'normal')
            if vol_regime == 'extreme' and regime_confidence < 0.8:
                veto_reasons.append("Extreme volatility with low regime confidence")

            # Determine decision
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                level.confidence = 0.0
                level.reasoning = f"Market context veto: {level.veto_reason}"
            else:
                level.decision = "PROCEED"
                level.confidence = regime_confidence * session_multiplier
                level.reasoning = f"Market context favorable (regime: {market_regime:.2f}, session_mult: {session_multiplier:.2f})"

            return level

        except Exception as e:
            level.decision = "VETO"
            level.veto_reason = f"Market context error: {str(e)}"
            level.confidence = 0.0
            level.reasoning = level.veto_reason
            return level

    def _level3_signal_quality(self, lightgbm_pred: Dict[str, Any],
                            market_context: Dict[str, Any],
                            level1: DecisionLevel,
                            level2: DecisionLevel) -> DecisionLevel:
        """
        Level 3: Signal Quality Assessment using LightGBM Signal Generator.

        Evaluates signal strength, confidence, and trading opportunity quality.
        """
        level = DecisionLevel(
            level=3,
            name="Signal Quality Assessment",
            model_type="lightgbm",
            weight=0.40,
            veto_power=True
        )

        try:
            # Extract LightGBM signal analysis
            print(f"lightgbm_pred ***: {lightgbm_pred}")
            if isinstance(lightgbm_pred, dict) and 'outputs' in lightgbm_pred:
                outputs = lightgbm_pred['outputs']
                signal_prob = outputs.get('signal_probability', 0.5)
                signal_strength = outputs.get('signal_strength', 0.5)
                confidence_score = outputs.get('confidence_score', 0.5)
                direction = outputs.get('direction_classification', 'HOLD')
                model_confidence = lightgbm_pred.get('confidence', 0.0) 
            else:
                signal_prob = lightgbm_pred.get('signal_probability', 0.5)
                signal_strength = lightgbm_pred.get('signal_strength', 0.5)
                confidence_score = lightgbm_pred.get('confidence_score', 0.5)
                direction = lightgbm_pred.get('direction_classification', 'HOLD')
                model_confidence = lightgbm_pred.get('confidence', 0.0)   

            # Final confidence to use in decisions
            signal_confidence = max(0.0, min(1.0, model_confidence))

            # Log signal analysis
            self.logger.info(f"     LightGBM Signal Analysis:")
            self.logger.info(f"       Signal Probability: {signal_prob:.3f}")
            self.logger.info(f"       Signal Strength: {signal_strength:.3f}")
            self.logger.info(f"       Confidence Score: {confidence_score:.3f}")
            self.logger.info(f"       Direction: {direction}")
            self.logger.info(f"       Model Confidence: {signal_confidence:.3f}")
            self.logger.info(f"       Minimum Threshold: {self.min_confidence_threshold:.3f}")

            # Signal quality checks
            veto_reasons = []

            # Check minimum confidence threshold
            self.logger.info(f"       Confidence Check: {signal_confidence:.3f} vs threshold {self.min_confidence_threshold:.3f}")
            if  0.35 <signal_confidence < self.min_confidence_threshold:
                reason = f"Signal confidence {signal_confidence:.3f} below threshold {self.min_confidence_threshold:.3f}"
                veto_reasons.append(reason)
                self.logger.info(f"       ❌ FAILED: {reason}")
            else:
                self.logger.info(f"       ✅ PASSED: Confidence above threshold")

                # Check signal consistency with previous levels
                self.logger.info(f"       Level Consistency Check:")
                self.logger.info(f"         Level 1 Confidence: {level1.confidence:.3f}")
                self.logger.info(f"         Level 2 Confidence: {level2.confidence:.3f}")

                if level1.confidence < 0.4:
                    reason = f"L1 veto: Weak base signal (L1: {level1.confidence:.3f}) despite confidence {signal_confidence:.3f}"
                    veto_reasons.append(reason)
                    self.logger.info(f"         ❌ FAILED: {reason}")

                if level2.confidence < 0.5:
                    reason = f"L2 veto: Unfavorable market context (L2: {level2.confidence:.3f}) despite confidence {signal_confidence:.3f}"
                    veto_reasons.append(reason)
                    self.logger.info(f"         ❌ FAILED: {reason}")

                if not veto_reasons:
                    self.logger.info(f"         ✅ PASSED: Signal consistency checks")


            # Check for signal divergence (if available)
            feature_importance = lightgbm_pred.get('feature_importance', {})
            if feature_importance:
                top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]
                if all(importance < 0.1 for _, importance in top_features):
                    veto_reasons.append("Weak feature importance in signal generation")

            # Determine decision and direction
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                # keep original signal_confidence for transparency
                level.confidence = signal_confidence
                level.reasoning = f"Signal quality veto: {level.veto_reason}"
            else:
                #if signal_confidence < 0.35:
                #    direction = "SELL"
                level.decision = direction if signal_prob > 0.5 else direction
                level.confidence = signal_confidence
                level.reasoning = f"Signal quality good (prob: {signal_prob:.3f}, conf: {signal_confidence:.3f})"

            return level

        except Exception as e:
            level.decision = "VETO"
            level.veto_reason = f"Signal quality error: {str(e)}"
            # keep as None to indicate error, not "0.0 confidence"
            level.confidence = None
            level.reasoning = level.veto_reason
            return level

    def _level4_final_integration(self, xgboost_pred: Dict[str, Any],
                                 market_context: Dict[str, Any],
                                 level1: DecisionLevel,
                                 level2: DecisionLevel,
                                 level3: DecisionLevel) -> DecisionLevel:
        """
        Level 4: Final Decision Integration using XGBoost Risk Manager.

        Integrates all previous levels and applies final risk adjustments.
        """
        level = DecisionLevel(
            level=4,
            name="Final Decision Integration",
            model_type="xgboost",
            weight=0.25,
            veto_power=False  # Final level adjusts but doesn't veto
        )

        try:
            # Extract XGBoost risk analysis
            risk_score = xgboost_pred.get('risk_score', 0.5)
            position_size_adj = xgboost_pred.get('position_size_adjustment', 1.0)
            print(f"level3.decision: {level3.decision}")
            # Integrate previous level decisions
            if level3.decision in ['BUY', 'SELL']:
                level.decision = level3.decision
            else:
                level.decision = "HOLD"

            # Calculate integrated confidence
            weighted_confidence = (
                level1.confidence * level1.weight +
                level2.confidence * level2.weight +
                level3.confidence * level3.weight
            ) / (level1.weight + level2.weight + level3.weight)

            # Apply risk adjustments
            risk_adjusted_confidence = weighted_confidence * (1 - risk_score * 0.5)

            level.confidence = max(0.0, min(1.0, risk_adjusted_confidence))
            level.reasoning = f"Risk-adjusted integration (risk: {risk_score:.3f}, pos_adj: {position_size_adj:.3f})"

            return level

        except Exception as e:
            level.decision = "HOLD"
            level.confidence = 0.0
            level.reasoning = f"Final integration error: {str(e)}"
            return level

    def _integrate_decisions(self, decision_levels: Dict[int, DecisionLevel],
                           model_predictions: Dict[str, Any],
                           market_context: Dict[str, Any]) -> HierarchicalDecision:
        """Integrate all decision levels into final hierarchical decision."""

        # Get final decision from Level 4
        final_level = decision_levels[4]
        final_decision = final_level.decision

        # Calculate overall confidence (weighted average)
        total_weight = sum(level.weight for level in decision_levels.values())
        overall_confidence = sum(
            level.confidence * level.weight for level in decision_levels.values()
        ) / total_weight if total_weight > 0 else 0.0

        # Calculate consensus score
        decisions = [level.decision for level in decision_levels.values() if level.decision != "VETO"]
        if len(decisions) > 1:
            consensus_score = len([d for d in decisions if d == final_decision]) / len(decisions)
        else:
            consensus_score = 1.0 if decisions else 0.0

        # Calculate risk score
        risk_score = 1.0 - overall_confidence

        # Determine entry timing
        if overall_confidence >= self.high_confidence_threshold:
            entry_timing = "IMMEDIATE"
        elif overall_confidence >= self.min_confidence_threshold:
            entry_timing = "PATIENT"
        else:
            entry_timing = "WAIT"

        # Calculate position sizing
        position_sizing = self._calculate_position_sizing(decision_levels, overall_confidence)

        # Calculate TP/SL levels
        tp_sl_levels = self._calculate_tp_sl_levels(decision_levels, model_predictions)

        # Log final hierarchical decision with comprehensive summary
        self.logger.info("=" * 60)
        self.logger.info("🎯 FINAL HIERARCHICAL DECISION SUMMARY")
        self.logger.info(f"   Decision: {final_decision}")
        self.logger.info(f"   Overall Confidence: {overall_confidence:.3f}")
        self.logger.info(f"   Consensus Score: {consensus_score:.3f}")
        self.logger.info(f"   Risk Score: {risk_score:.3f}")
        self.logger.info(f"   Position Sizing: {position_sizing:.3f}")
        self.logger.info(f"   Entry Timing: {entry_timing}")

        # Log TP/SL levels
        if tp_sl_levels:
            self.logger.info("   TP/SL Levels:")
            for level, value in tp_sl_levels.items():
                self.logger.info(f"     {level}: {value}")

        # Log level-by-level summary
        self.logger.info("   Level Summary:")
        for level_num, level_result in decision_levels.items():
            status = "✅ PASSED" if not level_result.veto_reason else f"❌ VETOED: {level_result.veto_reason}"
            self.logger.info(f"     Level {level_num} ({level_result.name}): {status} | Confidence: {level_result.confidence:.3f}")

        self.logger.info("=" * 60)

        return HierarchicalDecision(
            final_decision=final_decision,
            overall_confidence=overall_confidence,
            decision_levels=decision_levels,
            consensus_score=consensus_score,
            risk_score=risk_score,
            entry_timing=entry_timing,
            position_sizing=position_sizing,
            tp_sl_levels=tp_sl_levels,
            timestamp=datetime.now()
        )

    def _calculate_position_sizing(self, decision_levels: Dict[int, DecisionLevel],
                                 overall_confidence: float) -> float:
        """Calculate position sizing based on hierarchical decision."""
        base_size = 1.0

        # Confidence-based adjustment
        confidence_multiplier = 0.5 + (overall_confidence * 1.5)  # 0.5 to 2.0 range

        # Level-specific adjustments
        safety_multiplier = max(0.5, decision_levels[1].confidence)  # Safety check
        regime_multiplier = max(0.7, decision_levels[2].confidence)  # Market context
        signal_multiplier = max(0.8, decision_levels[3].confidence)  # Signal quality

        final_size = base_size * confidence_multiplier * safety_multiplier * regime_multiplier * signal_multiplier

        return max(0.1, min(2.0, final_size))  # Clamp between 0.1x and 2.0x

    def _calculate_tp_sl_levels(self, decision_levels: Dict[int, DecisionLevel],
                              model_predictions: Dict[str, Any]) -> Dict[str, float]:
        """Calculate TP/SL levels based on model predictions."""

        # Get base levels from LightGBM (primary signal generator)
        lightgbm_pred = model_predictions.get('lightgbm', {})
        base_tp1 = lightgbm_pred.get('tp1_distance', 15.0)
        base_tp2 = lightgbm_pred.get('tp2_distance', 30.0)
        base_sl = lightgbm_pred.get('sl_distance', 20.0)

        # Apply volatility adjustment from Linear model
        linear_pred = model_predictions.get('linear', {})
        vol_adjustment = linear_pred.get('volatility_adjustment', 1.0)

        # Apply regime-based adjustments from CatBoost
        catboost_pred = model_predictions.get('catboost', {})
        regime_multiplier = catboost_pred.get('regime_tp_multiplier', 1.0)

        # Apply risk adjustments from XGBoost
        xgboost_pred = model_predictions.get('xgboost', {})
        risk_multiplier = max(0.7, 1.0 - xgboost_pred.get('risk_score', 0.0) * 0.5)

        # Calculate final levels
        tp1_distance = base_tp1 * vol_adjustment * regime_multiplier * risk_multiplier
        tp2_distance = base_tp2 * vol_adjustment * regime_multiplier * risk_multiplier
        sl_distance = base_sl * vol_adjustment * risk_multiplier  # SL less affected by regime

        return {
            'tp1_distance': max(5.0, tp1_distance),
            'tp2_distance': max(10.0, tp2_distance),
            'sl_distance': max(8.0, sl_distance),
            'tp1_percentage': 0.4,  # 40% of position
            'tp2_percentage': 0.35,  # 35% of position
            'trailing_percentage': 0.25  # 25% for trailing
        }

    def _create_veto_decision(self, veto_level: DecisionLevel,
                            decision_levels: Dict[int, DecisionLevel],
                            timestamp: datetime) -> HierarchicalDecision:
        """Create a veto decision when a level vetoes the trade."""

        # Log comprehensive veto summary
        self.logger.info("=" * 60)
        self.logger.info("🚫 HIERARCHICAL DECISION VETOED")
        self.logger.info(f"   Vetoed by: {veto_level.name}")
        self.logger.info(f"   Veto Reason: {veto_level.veto_reason}")
        self.logger.info(f"   Veto Level Confidence: {veto_level.confidence:.3f}")

        # Show which levels passed before the veto
        self.logger.info("   Level Status:")
        for level_num, level_result in decision_levels.items():
            if level_result.veto_reason:
                self.logger.info(f"     Level {level_num} ({level_result.name}): ❌ VETOED - {level_result.veto_reason}")
                break
            else:
                self.logger.info(f"     Level {level_num} ({level_result.name}): ✅ PASSED - {level_result.reasoning}")

        self.logger.info("   Final Decision: VETO (No trade executed)")
        self.logger.info("=" * 60)

        return HierarchicalDecision(
            final_decision="VETO",
            overall_confidence=0.0,
            decision_levels=decision_levels,
            consensus_score=0.0,
            risk_score=1.0,
            entry_timing="WAIT",
            position_sizing=0.0,
            tp_sl_levels={},
            timestamp=timestamp
        )

    def _create_error_decision(self, error_msg: str, timestamp: datetime) -> HierarchicalDecision:
        """Create an error decision when the framework fails."""
        return HierarchicalDecision(
            final_decision="ERROR",
            overall_confidence=0.0,
            decision_levels={},
            consensus_score=0.0,
            risk_score=1.0,
            entry_timing="WAIT",
            position_sizing=0.0,
            tp_sl_levels={},
            timestamp=timestamp
        )
